package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE_IN;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_GOHOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.MSG_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.os.Handler;
import android.os.Looper;
import android.service.notification.StatusBarNotification;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.adapter.NotificationAdapter;
import com.yk.secondary.screen.settings.utils.Constant;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.view.MainHorizontalScrollView;
import com.yk.secondary.screen.settings.view.NotificationFrameLayout;
import com.yk.secondary.screen.settings.view.NotificationListView;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

public class NotificationViewManager implements MainHorizontalScrollView.OnUpSlideListener {

    private NotificationViewManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final NotificationViewManager instance = new NotificationViewManager();
    }

    public static NotificationViewManager getInstance() {
        return NotificationViewManager.SingletonHolder.instance;
    }

    //所有通知列表
    private final ArrayList<StatusBarNotification> mStatusBarNotificationList = new ArrayList<>();

    public ArrayList<StatusBarNotification> getStatusBarNotificationList() {
        return mStatusBarNotificationList;
    }

    private final Handler mGoHomeTimeHandler = new Handler(Looper.getMainLooper(), msg -> {
        if (msg.what == MSG_GO_HOME_TIME) {
            Log.i(TAG_LOG, "NotificationViewManager : mGoHomeTimeHandler MSG_GO_HOME_TIME .... ");
            setTopMargin(Constant.SCREEN_HEIGHT);
            onUpSlide();
            SecondaryScreenBitmapManager.getInstance().showWatchView();
        }
        return false;
    });

    public void sendGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
        int time = SPUtils.getInstance().getInt(KEY_SUBSCREEN_GOHOME_TIME, DEFAULT_GO_HOME_TIME);
        if (time != -1) {
            mGoHomeTimeHandler.sendEmptyMessageDelayed(MSG_GO_HOME_TIME, time);
        }
    }

    public void removeGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
    }

    private NotificationFrameLayout mRootView;

    private MainHorizontalScrollView mMainHorizontalScrollView;

    public void setMainHorizontalScrollView(MainHorizontalScrollView mainHorizontalScrollView) {
        this.mMainHorizontalScrollView = mainHorizontalScrollView;
        this.mMainHorizontalScrollView.setOnUpSlideListener(this);
    }

    public void setRootView(NotificationFrameLayout rootView) {
        this.mRootView = rootView;
        mRootView.setOnUpSlideListener(this);
        initViews();
        initData();
    }

    public NotificationFrameLayout getRootView() {
        return mRootView;
    }

    //是否完全显示
    private final AtomicBoolean mIsShow = new AtomicBoolean(false);

    public boolean isShow() {
        return mIsShow.get();
    }

    private void setIsShow(boolean show) {
        mIsShow.set(show);
        if (show) {
            sendGoHomeTimeMsg();
            SecondaryScreenBitmapManager.getInstance().removeGoHomeTimeMsg();
        } else {
            SecondaryScreenBitmapManager.getInstance().sendGoHomeTimeMsg();
        }
    }

    private FrameLayout.LayoutParams mLayoutParams;

    private float mTopMargin = Constant.SCREEN_HEIGHT;//消息距离顶部的距离

    private FrameLayout mChildRootView;

    private FrameLayout mFlNotiNoItems;

    private TextView mTvTip;

    private NotificationListView mNotificationListView;

    private NotificationAdapter mNotificationAdapter;

    public float getTopMargin() {
        return mTopMargin;
    }

    public void setTopMargin(float mTopMargin) {
        this.mTopMargin = mTopMargin;
    }

    @SuppressLint("InflateParams")
    private void initViews() {
        mChildRootView = (FrameLayout) SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_notification, null);
        mNotificationListView = mChildRootView.findViewById(R.id.list_noti);
        mFlNotiNoItems = mChildRootView.findViewById(R.id.notification_no_items);
        mTvTip = mChildRootView.findViewById(R.id.no_notification);
        mRootView.setNotificationListView(mNotificationListView);

        mNotificationListView.setDivider(null);
        mNotificationListView.setVerticalScrollBarEnabled(false);
        mNotificationAdapter = new NotificationAdapter();
        mNotificationListView.setAdapter(mNotificationAdapter);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Constant.SCREEN_WIDTH, (int) (App.getInstance().getContext().getResources().getDimension(R.dimen.noti_item_height)));
        FrameLayout footerView = (FrameLayout) SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.noti_foot, null);
        ImageView footerViewBtn = footerView.findViewById(R.id.noti_foot_btn);
        footerView.setLayoutParams(layoutParams);
        mRootView.setOnClickListener(footerViewBtn, id -> {
            mStatusBarNotificationList.clear();
            initData();
        });
        mNotificationListView.addFooterView(footerView);

        if (mRootView != null) {
            mRootView.removeAllViews();
            mLayoutParams = new FrameLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
            mLayoutParams.gravity = Gravity.TOP;
            mLayoutParams.topMargin = Constant.SCREEN_HEIGHT;
            mChildRootView.setLayoutParams(mLayoutParams);
            mRootView.addView(mChildRootView);
            mRootView.setVisibility(View.VISIBLE);
        }
    }

    public void initData() {
        Log.i(TAG_LOG, "NotificationViewManager initData: ");
        if (SecondaryScreenControlManager.getInstance().getScreenStatus() == STATUS_SCREEN_CLOSE || SecondaryScreenControlManager.getInstance().getScreenStatus() == STATUS_SCREEN_CLOSE_IN) {
            return;
        }
        WatchViewManager.getInstance().updateNotificationTipImageView();
        if (mStatusBarNotificationList.isEmpty()) {
            mFlNotiNoItems.setVisibility(View.VISIBLE);
            mNotificationListView.setVisibility(View.GONE);
        } else {
            mNotificationListView.setVisibility(View.VISIBLE);
            mFlNotiNoItems.setVisibility(View.GONE);
            int height = (int) (App.getInstance().getContext().getResources().getDimension(R.dimen.noti_item_height) * (mStatusBarNotificationList.size() + 1));
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Constant.SCREEN_WIDTH, height);
            mNotificationListView.setLayoutParams(layoutParams);
            mNotificationAdapter.notifyDataSetChanged();
        }
    }

    public void updateLanguage() {
        if (mTvTip != null) {
            mTvTip.setText(App.getInstance().getContext().getText(R.string.notification_no_items));
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    public void addStatusBarNotification(StatusBarNotification sbn) {
        Notification notification = sbn.getNotification();
        if (notification == null) return;

        CharSequence titleCS = notification.extras.getCharSequence(Notification.EXTRA_TITLE);
        CharSequence contentCS = notification.extras.getCharSequence(Notification.EXTRA_TEXT);

        String title = titleCS != null ? titleCS.toString() : null;
        String content = contentCS != null ? contentCS.toString() : null;

        Log.d(TAG_LOG, "addStatusBarNotification: title=" + title + ", content=" + content);
        if (TextUtils.isEmpty(title) && TextUtils.isEmpty(content)) {
            return;
        }

        int index = -1;
        for (int i = 0; i < mStatusBarNotificationList.size(); i++) {
            if (TextUtils.equals(mStatusBarNotificationList.get(i).getPackageName(), sbn.getPackageName())) {
                index = i;
                break;
            }
        }

        if (index != -1) {
            if (!compareStatusBarNotification(mStatusBarNotificationList.get(index), sbn)) {
                mStatusBarNotificationList.set(index, sbn);
            }
        } else {
            mStatusBarNotificationList.add(sbn);
        }
        initData();
    }

    public void removeStatusBarNotification(StatusBarNotification sbn) {
        if (sbn == null) {
            return;
        }

        Notification notification = sbn.getNotification();
        if (notification == null) {
            return;
        }
        Log.i(TAG_LOG, "NotificationViewManager removeStatusBarNotification: ");
        CharSequence titleCS = notification.extras.getCharSequence(Notification.EXTRA_TITLE);
        CharSequence contentCS = notification.extras.getCharSequence(Notification.EXTRA_TEXT);

        String title = titleCS != null ? titleCS.toString() : null;
        String content = contentCS != null ? contentCS.toString() : null;
        if (TextUtils.isEmpty(title) && TextUtils.isEmpty(content)) {
            return;
        }
        int index = -1;
        for (int i = 0; i < mStatusBarNotificationList.size(); i++) {
            if (TextUtils.equals(mStatusBarNotificationList.get(i).getPackageName(), sbn.getPackageName())) {
                index = i;
                break;
            }
        }

        if (index != -1) {
            mStatusBarNotificationList.remove(index);
        }
        initData();
    }

    private boolean compareStatusBarNotification(StatusBarNotification statusBarNotification1, StatusBarNotification statusBarNotification2) {
        if (!TextUtils.equals(statusBarNotification1.getPackageName(), statusBarNotification2.getPackageName())) {
            return false;
        }

        Notification n1 = statusBarNotification1.getNotification();
        Notification n2 = statusBarNotification2.getNotification();

        CharSequence title1CS = n1.extras.getCharSequence(Notification.EXTRA_TITLE);
        CharSequence title2CS = n2.extras.getCharSequence(Notification.EXTRA_TITLE);
        CharSequence content1CS = n1.extras.getCharSequence(Notification.EXTRA_TEXT);
        CharSequence content2CS = n2.extras.getCharSequence(Notification.EXTRA_TEXT);

        String title1 = title1CS != null ? title1CS.toString() : null;
        String title2 = title2CS != null ? title2CS.toString() : null;
        String content1 = content1CS != null ? content1CS.toString() : null;
        String content2 = content2CS != null ? content2CS.toString() : null;

        return TextUtils.equals(title1, title2) && TextUtils.equals(content1, content2);
    }

    private void upSlideNotificationView() {
        mLayoutParams.topMargin = (int) mTopMargin;
        mChildRootView.setLayoutParams(mLayoutParams);

        if (mTopMargin == 0) {
            setIsShow(true);
            if (mNotificationListView != null) {
                mNotificationListView.smoothScrollToPosition(0);
            }
        }

        if (mTopMargin == Constant.SCREEN_HEIGHT) {
            setIsShow(false);
        }
    }

    @Override
    public void onUpSlide() {
        upSlideNotificationView();
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "NotificationViewManager onDestroy: ");
        if (mMainHorizontalScrollView != null) {
            mMainHorizontalScrollView.setOnSlideListener(null);
        }

        setIsShow(false);

        mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        mGoHomeTimeHandler.removeCallbacksAndMessages(null);

        if (mRootView != null) {
            mRootView.clearClickListener();
            mRootView.setOnUpSlideListener(null);
            mRootView.removeAllViews();
            mRootView.setVisibility(View.GONE);
        }
    }

}
