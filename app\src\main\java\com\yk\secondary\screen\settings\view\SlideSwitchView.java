package com.yk.secondary.screen.settings.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;

public class SlideSwitchView extends FrameLayout {

    private ImageView mCenterImg, mBgImg;
    private TextView mLeftText, mRightText;
    private float startX;

    private SlideSwitchViewListener mSlideSwitchViewListener;

    public void setSlideSwitchViewListener(SlideSwitchViewListener slideSwitchViewListener) {
        this.mSlideSwitchViewListener = slideSwitchViewListener;
    }

    public SlideSwitchView(@NonNull Context context) {
        super(context);
        initView(context);
    }

    public SlideSwitchView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
        initAttrs(context, attrs);
    }

    public SlideSwitchView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
        initAttrs(context, attrs);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                startX = event.getRawX();
                return true;
            case MotionEvent.ACTION_MOVE:
                float dx = event.getRawX() - startX;
                startX = event.getRawX();
                int left = mCenterImg.getLeft() + (int) dx;
                // 限制View在屏幕内移动
                if (left < 0) {
                    left = 0;
                } else if (left > getWidth() - mCenterImg.getWidth()) {
                    left = getWidth() - mCenterImg.getWidth();
                }
                mCenterImg.layout(left, mCenterImg.getHeight() / 4, left + mCenterImg.getWidth(), mCenterImg.getHeight() + mCenterImg.getHeight() / 4);
                return true;
            case MotionEvent.ACTION_UP:
                if (mSlideSwitchViewListener != null) {
                    if (mCenterImg.getLeft() <= 30) {
                        mSlideSwitchViewListener.onSlideLeft();
                    }

                    if (mCenterImg.getLeft() >= 150) {
                        mSlideSwitchViewListener.onSlideRight();
                    }
                }

                mCenterImg.layout(getWidth() / 2 - mCenterImg.getWidth() / 2, mCenterImg.getHeight() / 4, getWidth() / 2 - mCenterImg.getWidth() / 2 + mCenterImg.getWidth(), mCenterImg.getHeight() + mCenterImg.getHeight() / 4);
                return true;
        }
        return true;
    }

    private void initView(final Context context) {
        LayoutInflater.from(context).inflate(R.layout.view_slide_switch, this, true);
        mCenterImg = findViewById(R.id.iv_center);
        mBgImg = findViewById(R.id.iv_bg);
        mLeftText = findViewById(R.id.tv_left);
        mRightText = findViewById(R.id.tv_right);
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        @SuppressLint("CustomViewStyleable") TypedArray mTypedArray = context.obtainStyledAttributes(attrs, R.styleable.Slide_view);
        String leftText = mTypedArray.getString(R.styleable.Slide_view_left_text);
        if (!TextUtils.isEmpty(leftText)) {
            mLeftText.setText(leftText);
        }

        String rightText = mTypedArray.getString(R.styleable.Slide_view_right_text);
        if (!TextUtils.isEmpty(rightText)) {
            mRightText.setText(rightText);
        }
        int centerId = mTypedArray.getResourceId(R.styleable.Slide_view_center_img, -1);
        if (centerId != -1) {
            mCenterImg.setImageResource(centerId);
        }

        int bgId = mTypedArray.getResourceId(R.styleable.Slide_view_view_bg, -1);
        if (bgId != -1) {
            mBgImg.setImageResource(bgId);
        }

        mTypedArray.recycle();
    }

    public interface SlideSwitchViewListener {
        void onSlideLeft();

        void onSlideRight();
    }

}
