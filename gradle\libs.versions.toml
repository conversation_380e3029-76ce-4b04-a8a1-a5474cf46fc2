[versions]
agp = "8.6.0"
cameraCamera2Version = "1.3.4"
cameraCore = "1.3.4"
cameraLifecycleVersion = "1.3.4"
cameraVideoVersion = "1.3.4"
cameraViewVersion = "1.3.4"
cameraExtensionsVersion = "1.3.4"
guavaVersion = "32.0.1-jre"
glideVersion = "4.12.0"
compilerVersion = "4.12.0"
jsr305 = "3.0.2"
appcompat = "1.6.1"
material = "1.10.0"
activity = "1.8.0"
constraintlayout = "2.1.4"

[libraries]
androidx-camera-extensions = { module = "androidx.camera:camera-extensions", version.ref = "cameraExtensionsVersion" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "cameraViewVersion" }
androidx-camera-video = { module = "androidx.camera:camera-video", version.ref = "cameraVideoVersion" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "cameraLifecycleVersion" }
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "cameraCamera2Version" }
camera-core = { module = "androidx.camera:camera-core", version.ref = "cameraCore" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "compilerVersion" }
github-glide = { module = "com.github.bumptech.glide:glide", version.ref = "glideVersion" }
google-guava = { module = "com.google.guava:guava", version.ref = "guavaVersion" }
jsr305 = { module = "com.google.code.findbugs:jsr305", version.ref = "jsr305" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }

