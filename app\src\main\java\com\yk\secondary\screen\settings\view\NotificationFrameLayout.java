package com.yk.secondary.screen.settings.view;

import static android.view.MotionEvent.ACTION_CANCEL;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.NotificationViewManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;
import com.yk.secondary.screen.settings.utils.Constant;

import java.util.HashMap;

public class NotificationFrameLayout extends FrameLayout {

    public NotificationFrameLayout(@NonNull Context context) {
        super(context);
        initDoubleTapListener(context);
    }

    public NotificationFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initDoubleTapListener(context);
    }

    public NotificationFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initDoubleTapListener(context);
    }

    private final HashMap<Integer, CameraFrameLayout.OnClickListener> mClickViews = new HashMap<>();

    public void clearClickListener() {
        mClickViews.clear();
    }

    public void setOnClickListener(@NonNull View view, CameraFrameLayout.OnClickListener listener) {
        mClickViews.put(view.getId(), listener);
    }

    private int mStartYPos;

    private MainHorizontalScrollView.OnUpSlideListener mOnUpSlideListener;

    private GestureDetector mGestureDetector;

    private boolean isDownSlide = false;
    private boolean isUpSlide = false;
    private boolean mNotificationListViewScrollTop = true;

    private NotificationListView mNotificationListView;

    public void setNotificationListView(NotificationListView listView) {
        this.mNotificationListView = listView;
    }

    public void setOnUpSlideListener(MainHorizontalScrollView.OnUpSlideListener onUpSlideListener) {
        this.mOnUpSlideListener = onUpSlideListener;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mGestureDetector != null) {
            mGestureDetector.onTouchEvent(event);
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                NotificationViewManager.getInstance().removeGoHomeTimeMsg();
                Log.i(TAG_LOG, "onTouchEvent: NotificationFrameLayout");
                SecondaryScreenControlManager.getInstance().removeSecondaryScreenSleepMsg();
                mStartYPos = (int) event.getY();
                if (NotificationViewManager.getInstance().isShow() && mNotificationListView.getVisibility() == VISIBLE) {
                    mNotificationListView.onTouchEvent(event);
                }
                if (mNotificationListView.getVisibility() == VISIBLE) {
                    mNotificationListViewScrollTop = mNotificationListView.isScrollTop();
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (NotificationViewManager.getInstance().isShow() && mNotificationListView.getVisibility() == VISIBLE) {
                    mNotificationListView.onTouchEvent(event);
                }
                break;
            case MotionEvent.ACTION_UP:
            case ACTION_CANCEL: {
                isDownSlide = false;
                isUpSlide = false;
                NotificationViewManager.getInstance().sendGoHomeTimeMsg();
                SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
                if (NotificationViewManager.getInstance().isShow() && mNotificationListView.getVisibility() == VISIBLE) {
                    mNotificationListView.onTouchEvent(event);
                }

                if (!mNotificationListViewScrollTop) {
                    return true;
                }

                if (Math.abs((event.getY() - mStartYPos)) > (float) Constant.SCREEN_HEIGHT / 12) {
                    if (event.getY() - mStartYPos > 0) {
                        if (mOnUpSlideListener != null) {
                            NotificationViewManager.getInstance().setTopMargin(Constant.SCREEN_HEIGHT);
                            mOnUpSlideListener.onUpSlide();
                        }
                    }
                } else {
                    if (mOnUpSlideListener != null) {
                        NotificationViewManager.getInstance().setTopMargin(0);
                        mNotificationListView.setScrollTotalY(0);
                        mOnUpSlideListener.onUpSlide();
                    }
                }
            }
        }
        return true;
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {

            @Override
            public boolean onSingleTapUp(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "MessageFrameLayout onSingleTapUp: ");
                try {
                    float x = e.getX();
                    float y = e.getY();
                    if (mClickViews.isEmpty()) {
                        return super.onSingleTapUp(e);
                    }
                    mClickViews.forEach((id, listener) -> {
                        if (id == R.id.noti_foot_btn) {
                            if (x >= 82 && x <= 158 && y >= 161 && y <= 227 && mNotificationListView.isScrollBottom()) {
                                mNotificationListViewScrollTop = true;
                                listener.onClick(id);
                            }
                        }
                    });
                } catch (Exception exception) {
                    Log.i(TAG_LOG, "MessageFrameLayout onSingleTapUp: exception ");
                }
                return super.onSingleTapUp(e);
            }

            @Override
            public boolean onScroll(@Nullable MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
                if (!isDownSlide && !isUpSlide && distanceY < 0) {
                    isDownSlide = true;
                }

                if (!isUpSlide && !isDownSlide && distanceY > 0) {
                    isUpSlide = true;
                }

                //如果滑动到顶部，在往下滑则消息界面整体往下滑动
                if (!mNotificationListViewScrollTop) {
                    return true;
                }

                if (isDownSlide && Math.abs(distanceX) < Math.abs(distanceY)) {
                    int topMargin = (int) (NotificationViewManager.getInstance().getTopMargin() - distanceY);
                    if (topMargin <= 0) {
                        topMargin = 0;
                    }
                    NotificationViewManager.getInstance().setTopMargin(topMargin);
                    if (mOnUpSlideListener != null) {
                        mOnUpSlideListener.onUpSlide();
                    }
                }
                return super.onScroll(e1, e2, distanceX, distanceY);
            }
        });
    }
}
