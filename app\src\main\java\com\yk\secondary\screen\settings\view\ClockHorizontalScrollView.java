package com.yk.secondary.screen.settings.view;

import static android.view.MotionEvent.ACTION_CANCEL;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_WATCH_SELECTION;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CLOCK_SELECT;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_GOHOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.MSG_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.ClockListViewManager;
import com.yk.secondary.screen.settings.utils.SPUtils;

import java.util.ArrayList;

public class ClockHorizontalScrollView extends HorizontalScrollView {

    private final ArrayList<Integer> mImgResIds = new ArrayList<>();

    private TextView mTvTag;

    private LinearLayout mChildGroup = null;

    private int mStartPos;

    private int mCurPage = 1;

    public int getCurPage() {
        return mCurPage;
    }

    public void setChildGroup(LinearLayout mChildGroup) {
        this.mChildGroup = mChildGroup;
    }

    public void setTvTag(TextView mTvTag) {
        this.mTvTag = mTvTag;
    }

    public ClockHorizontalScrollView(Context mContext, AttributeSet attrs, int defStyle) {
        super(mContext, attrs, defStyle);
    }

    public ClockHorizontalScrollView(Context mContext, AttributeSet attrs) {
        super(mContext, attrs);
    }

    public ClockHorizontalScrollView(Context mContext) {
        super(mContext);
    }

    private onScrollListener mOnScrollListener;

    public void setOnScrollListener(onScrollListener onScrollListener) {
        this.mOnScrollListener = onScrollListener;
    }

    private final Handler mGoHomeTimeHandler = new Handler(Looper.getMainLooper(), msg -> {
        if (msg.what == MSG_GO_HOME_TIME) {
            Log.i(TAG_LOG, "ClockHorizontalScrollView : mGoHomeTimeHandler MSG_GO_HOME_TIME .... ");
            ClockListViewManager.getInstance().hideClockListView();
        }
        return false;
    });

    public void sendGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
        int time = SPUtils.getInstance().getInt(KEY_SUBSCREEN_GOHOME_TIME, DEFAULT_GO_HOME_TIME);
        if (time != -1) {
            mGoHomeTimeHandler.sendEmptyMessageDelayed(MSG_GO_HOME_TIME, time);
        }
    }

    public void removeGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
    }

    public void initImageView() {
        mImgResIds.add(R.drawable.icon_watch_style1);
        mImgResIds.add(R.drawable.icon_watch_style2);
        mImgResIds.add(R.drawable.icon_watch_style3);
        mImgResIds.add(R.drawable.icon_watch_style4);
        mImgResIds.add(R.drawable.icon_watch_style5);
        mImgResIds.add(R.drawable.icon_watch_style6);
        mImgResIds.add(R.drawable.icon_watch_style7);
        mImgResIds.add(R.drawable.icon_watch_style8);
        mImgResIds.add(R.drawable.icon_watch_style9);
        mImgResIds.add(R.drawable.icon_watch_style10);
        mCurPage = SPUtils.getInstance().getInt(KEY_SUBSCREEN_CLOCK_SELECT, DEFAULT_WATCH_SELECTION) + 1;
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                (int) App.getInstance().getResources().getDimension(R.dimen.clock_list_width),
                (int) App.getInstance().getResources().getDimension(R.dimen.clock_list_height));
        try {
            for (int resId : mImgResIds) {
                ImageView imageView = new ImageView(App.getInstance().getContext());
                imageView.setScaleType(ImageView.ScaleType.FIT_XY);
                imageView.setLayoutParams(layoutParams);
                imageView.setImageResource(resId);
                mChildGroup.addView(imageView);
            }
            mChildGroup.addOnLayoutChangeListener(new OnLayoutChangeListener() {
                @Override
                public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                    mChildGroup.removeOnLayoutChangeListener(this);
                    scrollToPage(mCurPage - 1);
                }
            });
        } catch (Exception e) {
            Log.i(TAG_LOG, "PhotoHorizontalScrollView initImageView: Exception " + e.getMessage());
        }
    }

    @SuppressLint("SetTextI18n")
    private void scrollToPage(int index) {
        if (mChildGroup == null) return;
        if (index < 0 || index >= mChildGroup.getChildCount()) return;
        scrollTo(getChildLeft(index), 0);
        if (mOnScrollListener != null) {
            mOnScrollListener.onScrolled(index + 1);
        }
        if (mTvTag != null) {
            mTvTag.setText((index + 1) + "/" + mImgResIds.size());
        }
    }

    @SuppressLint("SetTextI18n")
    private void smoothScrollToPage(int index) {
        if (mChildGroup == null) return;
        if (index < 0 || index >= mChildGroup.getChildCount()) return;
        smoothScrollTo(getChildLeft(index), 0);
        if (mTvTag != null) {
            mTvTag.setText((index) + "/" + mImgResIds.size());
        }
    }

    @SuppressLint("SetTextI18n")
    public synchronized void prevPage() {
        if (mCurPage <= 1) {
            return;
        }
        mCurPage--;
        smoothScrollToPage(mCurPage - 1);
        if (mOnScrollListener != null) {
            mOnScrollListener.onScrolled(mCurPage);
        }
        if (mTvTag != null) {
            mTvTag.setText(mCurPage + "/" + mImgResIds.size());
        }
    }

    @SuppressLint("SetTextI18n")
    public synchronized void nextPage() {
        if (mCurPage >= mImgResIds.size()) {
            return;
        }
        mCurPage++;
        smoothScrollToPage(mCurPage - 1);
        if (mOnScrollListener != null) {
            mOnScrollListener.onScrolled(mCurPage);
        }
        if (mTvTag != null) {
            mTvTag.setText(mCurPage + "/" + mImgResIds.size());
        }
    }

    private int getChildLeft(int index) {
        if (index >= 0 && mChildGroup != null) {
            if (index < mChildGroup.getChildCount()) return mChildGroup.getChildAt(index).getLeft();
        }
        return 0;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        try {
            switch (ev.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    removeGoHomeTimeMsg();
                    mStartPos = (int) ev.getX();
                    break;
                case MotionEvent.ACTION_UP:
                case ACTION_CANCEL: {
                    sendGoHomeTimeMsg();
                    if (mStartPos != 0) {
                        if (ev.getX() - mStartPos > 0) {
                            prevPage();
                        } else if (ev.getX() - mStartPos < 0) {
                            nextPage();
                        }
                    }
                    return true;
                }
            }
            return super.onTouchEvent(ev);
        } catch (Exception e) {
            Log.e(TAG_LOG, "ClockHorizontalScrollView onTouchEvent error: " + e.getMessage());
            return false;
        }
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "PhotoHorizontalScrollView onDestroy: ");
        mImgResIds.clear();
        mStartPos = 0;
        if (mChildGroup != null) {
            mChildGroup.removeAllViews();
        }
        mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        mGoHomeTimeHandler.removeCallbacksAndMessages(null);
    }

    public interface onScrollListener {
        void onScrolled(int page);
    }
}