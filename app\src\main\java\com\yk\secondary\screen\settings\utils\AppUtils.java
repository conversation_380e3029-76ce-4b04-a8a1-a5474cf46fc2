package com.yk.secondary.screen.settings.utils;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.yk.framework.lib.NotificationManagerUtils;
import com.yk.secondary.screen.settings.App;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class AppUtils {

    @NonNull
    public static List<AppInfo> getAllAppsInfo() {
        List<AppInfo> list = new ArrayList<>();
        PackageManager pm = App.getInstance().getContext().getPackageManager();
        if (pm == null) return list;
        Intent intent = new Intent("android.intent.action.MAIN");
        intent.addCategory("android.intent.category.LAUNCHER");
        @SuppressLint("QueryPermissionsNeeded") List<ResolveInfo> installedPackages = pm.queryIntentActivities(intent, 1);
        for (ResolveInfo pi : installedPackages) {
            list.add(getBean(pm, pi));
        }
        return list;
    }

    @NonNull
    public static String getAppName(final String packageName) {
        if (TextUtils.isEmpty(packageName)) return "";
        try {
            PackageManager pm = App.getInstance().getContext().getPackageManager();
            PackageInfo pi = pm.getPackageInfo(packageName, 0);
            return pi == null ? "" : pi.applicationInfo.loadLabel(pm).toString();
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return "";
        }
    }

    @Nullable
    public static Drawable getAppIcon(final String packageName) {
        if (TextUtils.isEmpty(packageName)) return null;
        try {
            PackageManager pm = App.getInstance().getContext().getPackageManager();
            PackageInfo pi = pm.getPackageInfo(packageName, 0);
            return pi == null ? null : pi.applicationInfo.loadIcon(pm);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return null;
        }
    }

    @NonNull
    public static List<AppInfo> getAppsInfo() {
        List<AppInfo> list = new ArrayList<>();
        NotificationManagerUtils notificationManagerUtils = new NotificationManagerUtils();
        PackageManager pm = App.getInstance().getContext().getPackageManager();
        if (pm == null) return list;
        Intent intent = new Intent("android.intent.action.MAIN");
        intent.addCategory("android.intent.category.LAUNCHER");
        List<ResolveInfo> installedPackages = pm.queryIntentActivities(intent, 1);
        for (ResolveInfo pi : installedPackages) {
            try {
                int uid = pm.getPackageInfo(pi.activityInfo.packageName, 0).applicationInfo.uid;
                boolean banned = notificationManagerUtils.getNotificationsBanned(pi.activityInfo.packageName,uid);
                if (banned) {
                    continue;
                }
                AppInfo ai = getBean(pm, pi);
                list.add(ai);
            } catch (PackageManager.NameNotFoundException e) {
                e.printStackTrace();
            }
        }
        return list;
    }

    public static boolean isAppInstalled(final String pkgName) {
        if (TextUtils.isEmpty(pkgName)) return false;
        PackageManager pm = App.getInstance().getContext().getPackageManager();
        try {
            return pm.getApplicationInfo(pkgName, 0).enabled;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private static AppInfo getBean(final PackageManager pm, final ResolveInfo pi) {
        if (pi == null) return null;
        String packageName = pi.activityInfo.packageName;
        String name = pi.loadLabel(pm).toString();
        Drawable icon = pi.loadIcon(pm);
        return new AppInfo(packageName, name, icon);
    }

    public static class AppInfo {

        private String packageName;
        private String name;
        private Drawable icon;

        public AppInfo() {
        }

        public AppInfo(String packageName, String name, Drawable icon) {
            this.setName(name);
            this.setIcon(icon);
            this.setPackageName(packageName);
        }

        public Drawable getIcon() {
            return icon;
        }

        public void setIcon(final Drawable icon) {
            this.icon = icon;
        }

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(final String packageName) {
            this.packageName = packageName;
        }

        public String getName() {
            return name;
        }

        public void setName(final String name) {
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            AppInfo appInfo = (AppInfo) o;
            return Objects.equals(packageName, appInfo.packageName);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(packageName);
        }
    }
}
