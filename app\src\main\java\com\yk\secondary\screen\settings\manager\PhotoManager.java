package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.ThreadUtils;
import com.yk.secondary.screen.settings.view.PhotoFrameLayout;
import com.yk.secondary.screen.settings.view.PhotoHorizontalScrollView;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

public class PhotoManager {

    private PhotoManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final PhotoManager instance = new PhotoManager();
    }

    public static PhotoManager getInstance() {
        return SingletonHolder.instance;
    }

    private PhotoFrameLayout mPhotoRootView;

    public PhotoFrameLayout getPhotoRootView() {
        return mPhotoRootView;
    }

    public void setPhotoRootView(PhotoFrameLayout mPhotoRootView) {
        this.mPhotoRootView = mPhotoRootView;
    }

    private final AtomicBoolean mIsShow = new AtomicBoolean(false);

    public boolean isShow() {
        return mIsShow.get();
    }

    private void setIsShow(boolean show) {
        mIsShow.set(show);
        if (show) {
            SecondaryScreenBitmapManager.getInstance().removeGoHomeTimeMsg();
        } else {
            SecondaryScreenBitmapManager.getInstance().sendGoHomeTimeMsg();
        }
    }

    private PhotoHorizontalScrollView mPhotoHorizontalScrollView;

    public void showPhotoView() {
        if (mPhotoRootView == null) {
            return;
        }
        Log.i(TAG_LOG, "PhotoManager showPhotoView: ");
        ThreadUtils.executeByCpu(new ThreadUtils.SimpleTask<ArrayList<Uri>>() {
            @Override
            public ArrayList<Uri> doInBackground() {
                return getRecentPhotos();
            }

            @Override
            public void onSuccess(ArrayList<Uri> result) {
                Log.i(TAG_LOG, "PhotoManager showPhotoView getRecentPhotos onSuccess: ");
                showPhotoView(result);
            }
        }, Thread.MAX_PRIORITY);
    }

    private void showPhotoView(ArrayList<Uri> result) {
        if (result.isEmpty()) {
            setIsShow(false);
            return;
        }

        @SuppressLint("InflateParams") View view = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_photo, null);
        mPhotoHorizontalScrollView = view.findViewById(R.id.photo_scroll_view);
        mPhotoHorizontalScrollView.setChildGroup(view.findViewById(R.id.container_photo));
        mPhotoHorizontalScrollView.setTvTag(view.findViewById(R.id.tv_indicator));
        mPhotoHorizontalScrollView.setImgUriList(result);
        mPhotoHorizontalScrollView.initImageView();
        mPhotoRootView.setPhotoHorizontalScrollView(mPhotoHorizontalScrollView);
        ImageView backImg = view.findViewById(R.id.iv_back);
        mPhotoRootView.setOnClickListener(backImg, id -> {
            if (id == R.id.iv_back) {
                onDestroy(true);
            }
        });
        mPhotoRootView.removeAllViews();
        mPhotoRootView.addView(view);
        mPhotoRootView.setVisibility(View.VISIBLE);
        setIsShow(true);
    }

    public void onDestroy(boolean isShowCameraView) {
        setIsShow(false);
        if (mPhotoHorizontalScrollView != null) {
            mPhotoHorizontalScrollView.onDestroy();
            mPhotoHorizontalScrollView = null;
        }

        if (mPhotoRootView != null) {
            mPhotoRootView.clearClickListener();
            mPhotoRootView.removeAllViews();
            mPhotoRootView.setVisibility(View.GONE);
        }

        if (isShowCameraView) {
            //显示相机页面
            ThreadUtils.runOnUiThread(() -> CameraManager.getInstance().showCameraView());
        }
    }

    private ArrayList<Uri> getRecentPhotos() {
        ArrayList<Uri> uris = new ArrayList<>();
        Cursor cursor = null;
        try {
            String queryPath = MediaStore.Images.Media.RELATIVE_PATH;
            String sortOrder = MediaStore.Images.Media._ID + " DESC";
            String selection = queryPath + "=? ";
            Bundle queryBundle = new Bundle();
            queryBundle.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, selection);
            queryBundle.putStringArray(ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS, new String[]{"DCIM/MiniScreen/"});
            queryBundle.putString(ContentResolver.QUERY_ARG_SQL_SORT_ORDER, sortOrder);
            queryBundle.putInt(ContentResolver.QUERY_ARG_LIMIT, 10);
            cursor = App.getInstance().getContext().getContentResolver().query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, new String[]{MediaStore.Files.FileColumns._ID}, queryBundle, null);
            if (cursor != null && cursor.moveToNext()) {
                do {
                    //查出id
                    @SuppressLint("Range") int id = cursor.getInt(cursor.getColumnIndex(MediaStore.Images.Media._ID));
                    //根据id查询URI
                    Uri uri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);
                    uris.add(uri);
                } while (cursor.moveToNext());
            }

        } catch (Exception e) {
            Log.i(TAG_LOG, "PhotoManager getRecentPhotos: Exception " + e.getMessage());
        } finally {
            //关闭查询
            if (cursor != null) {
                cursor.close();
            }
        }
        return uris;
    }
}
