package com.yk.secondary.screen.settings.view;

import static android.view.MotionEvent.ACTION_CANCEL;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.RemindersViewManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;

import java.util.HashMap;

public class RemindersFrameLayout extends FrameLayout {

    private GestureDetector mGestureDetector;

    private final HashMap<Integer, OnClickListener> mClickViews = new HashMap<>();

    public void clearClickListener() {
        mClickViews.clear();
    }

    public RemindersFrameLayout(@NonNull Context context) {
        super(context);
        initDoubleTapListener(context);
    }

    public RemindersFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initDoubleTapListener(context);
    }

    public RemindersFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initDoubleTapListener(context);
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapUp(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "RemindersFrameLayout onSingleTapUp: ");
                try {
                    float x = e.getX();
                    float y = e.getY();
                    if (mClickViews.isEmpty()) {
                        return super.onSingleTapUp(e);
                    }
                    mClickViews.forEach((id, listener) -> {
                        if (id == R.id.iv_hangup) {
                            if (x >= 82 && x <= 158 && y >= 150 && y <= 226) {
                                listener.onClick(id);
                            }
                        }

                    });
                } catch (Exception exception) {
                    Log.i(TAG_LOG, "RemindersFrameLayout onSingleTapUp: exception " + exception.getMessage());
                }
                return super.onSingleTapUp(e);
            }
        });
    }

    public void setOnClickListener(@NonNull View view, OnClickListener listener) {
        mClickViews.put(view.getId(), listener);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mGestureDetector != null) {
            mGestureDetector.onTouchEvent(event);
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (TextUtils.equals(TelephonyManager.EXTRA_STATE_OFFHOOK, RemindersViewManager.getInstance().getPhotoState())) {
                    Log.i(TAG_LOG, "onTouchEvent: RemindersFrameLayout ");
                    SecondaryScreenControlManager.getInstance().removeSecondaryScreenSleepMsg();
                }
                break;
            case MotionEvent.ACTION_UP:
            case ACTION_CANCEL: {
                if (TextUtils.equals(TelephonyManager.EXTRA_STATE_OFFHOOK, RemindersViewManager.getInstance().getPhotoState())) {
                    SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
                }
                return super.onTouchEvent(event);
            }
        }
        return super.onTouchEvent(event);
    }

    public interface OnClickListener {
        void onClick(int id);
    }
}
