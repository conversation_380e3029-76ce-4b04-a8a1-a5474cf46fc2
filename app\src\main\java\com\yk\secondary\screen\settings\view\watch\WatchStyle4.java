package com.yk.secondary.screen.settings.view.watch;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.util.Log;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.Calendar;

public class WatchStyle4 extends BaseWatchStyle {

    private ImageView mIvWeek, mIvDate1, mIvDate2, mIvTimeH1, mIvTimeH2, mIvTimeM1, mIvTimeM2;

    public WatchStyle4() {
        super();
    }

    @Override
    public void initViews() {
        mRootView = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_bitmap_watch_style4, null);

        mIvWeek = mRootView.findViewById(R.id.iv_week);
        setWeek();

        mIvDate1 = mRootView.findViewById(R.id.iv_date_1);
        mIvDate2 = mRootView.findViewById(R.id.iv_date_2);
        setDate();


        mIvTimeH1 = mRootView.findViewById(R.id.iv_time_h_1);
        mIvTimeH2 = mRootView.findViewById(R.id.iv_time_h_2);
        setTimeH();

        mIvTimeM1 = mRootView.findViewById(R.id.iv_time_m_1);
        mIvTimeM2 = mRootView.findViewById(R.id.iv_time_m_2);
        setTimeM();
    }

    @Override
    public int getStyleValue() {
        return 4;
    }

    @Override
    public void updateTimeChanged() {
        setTimeH();
        setTimeM();
        setWeek();
        setDate();
    }

    @Override
    public void updateDateChanged() {
        setTimeH();
        setTimeM();
        setWeek();
        setDate();
    }

    @Override
    public void updateCurrentLevelChanged(BatteryState batteryState) {

    }

    private void setTimeH() {
        String h = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH"));
        try {
            if (mIvTimeH1 != null) {
                mIvTimeH1.setImageResource(getHourOrMinImageResource(h.charAt(0)));
            }

            if (mIvTimeH2 != null) {
                mIvTimeH2.setImageResource(getHourOrMinImageResource(h.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeH: Exception " + e.getMessage());
        }
    }

    private void setTimeM() {
        String m = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("mm"));
        try {
            if (mIvTimeM1 != null) {
                mIvTimeM1.setImageResource(getHourOrMinImageResource(m.charAt(0)));
            }

            if (mIvTimeM2 != null) {
                mIvTimeM2.setImageResource(getHourOrMinImageResource(m.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeM: Exception " + e.getMessage());
        }
    }

    private void setDate() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("dd"));
        try {
            if (mIvDate1 != null) {
                mIvDate1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvDate2 != null) {
                mIvDate2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setDays: Exception " + e.getMessage());
        }
    }

    private void setWeek() {
        if (mIvWeek != null) {
            mIvWeek.setImageResource(getWeekImageResource());
        }
    }

    private int getWeekImageResource() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        boolean isChinese = Utils.isChinese(App.getInstance().getContext());
        switch (dayOfWeek) {
            case 1:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_sun_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_sun_en;
                }
            case 2:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_mon_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_mon_en;
                }
            case 3:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_tue_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_tue_en;
                }
            case 4:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_wed_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_wed_en;
                }
            case 5:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_thu_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_thu_en;
                }
            case 6:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_fri_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_fri_en;
                }
            case 7:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_sat_en;
                }
            default:
                if (isChinese) {
                    return R.drawable.icon_watch_style4_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style4_week_sat_en;
                }
        }
    }

    private int getDateImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style4_date_0;
            case '1':
                return R.drawable.icon_watch_style4_date_1;
            case '2':
                return R.drawable.icon_watch_style4_date_2;
            case '3':
                return R.drawable.icon_watch_style4_date_3;
            case '4':
                return R.drawable.icon_watch_style4_date_4;
            case '5':
                return R.drawable.icon_watch_style4_date_5;
            case '6':
                return R.drawable.icon_watch_style4_date_6;
            case '7':
                return R.drawable.icon_watch_style4_date_7;
            case '8':
                return R.drawable.icon_watch_style4_date_8;
            case '9':
                return R.drawable.icon_watch_style4_date_9;
            default:
                return R.drawable.icon_watch_style4_date_0;
        }
    }
    
    private int getHourOrMinImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style4_hour_min_0;
            case '1':
                return R.drawable.icon_watch_style4_hour_min_1;
            case '2':
                return R.drawable.icon_watch_style4_hour_min_2;
            case '3':
                return R.drawable.icon_watch_style4_hour_min_3;
            case '4':
                return R.drawable.icon_watch_style4_hour_min_4;
            case '5':
                return R.drawable.icon_watch_style4_hour_min_5;
            case '6':
                return R.drawable.icon_watch_style4_hour_min_6;
            case '7':
                return R.drawable.icon_watch_style4_hour_min_7;
            case '8':
                return R.drawable.icon_watch_style4_hour_min_8;
            case '9':
                return R.drawable.icon_watch_style4_hour_min_9;
            default:
                return R.drawable.icon_watch_style4_hour_min_0;
        }
    }
}
