<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="240px"
    android:layout_height="240px"
    android:background="@android:color/black">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_dialer_incoming" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8px"
            android:includeFontPadding="false"
            android:text="@string/Incoming_call"
            android:textColor="@android:color/white"
            android:textSize="20px" />
    </LinearLayout>


    <TextView
        android:id="@+id/tv_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="66px"
        android:textColor="@android:color/white"
        android:textSize="32px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:maxLines="2"
        android:ellipsize="end"/>


    <com.yk.secondary.screen.settings.view.SlideSwitchView
        android:id="@+id/ssv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20px"
        app:center_img="@drawable/slide_switch_view_center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:left_text="@string/decline"
        app:right_text="@string/answer"
        app:view_bg="@drawable/slide_switch_view_bg" />

    <FrameLayout
        android:id="@+id/fl_phone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="8px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/iv_hangup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="160px"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/icon_phone_handup" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:textColor="#99FFFFFF"
            android:textSize="20px" />

    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>