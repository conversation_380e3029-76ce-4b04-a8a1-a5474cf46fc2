package com.yk.secondary.screen.settings.adapter;

import android.app.Notification;
import android.service.notification.StatusBarNotification;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.NotificationViewManager;
import com.yk.secondary.screen.settings.utils.AppUtils;

public class NotificationAdapter extends BaseAdapter {

    @Override
    public StatusBarNotification getItem(int position) {
        return NotificationViewManager.getInstance().getStatusBarNotificationList().get(position);
    }

    @Override
    public int getCount() {
        return NotificationViewManager.getInstance().getStatusBarNotificationList().size();
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        View view;
        NotificationAdapter.ViewHolder viewHolder;
        if (convertView == null) {
            viewHolder = new NotificationAdapter.ViewHolder();
            view = LayoutInflater.from(App.getInstance().getContext()).inflate(R.layout.item_notification, (ViewGroup) null);
            viewHolder.noti_ic = view.findViewById(R.id.noti_ic);
            viewHolder.noti_title = view.findViewById(R.id.noti_title);
            viewHolder.noti_content = view.findViewById(R.id.noti_content);
            view.setTag(viewHolder);
        } else {
            view = convertView;
            viewHolder = (NotificationAdapter.ViewHolder) convertView.getTag();
        }

        StatusBarNotification statusBarNotification = NotificationViewManager.getInstance().getStatusBarNotificationList().get(position);
        Notification notification = statusBarNotification.getNotification();
        if (notification != null) {
            CharSequence titleCS = notification.extras.getCharSequence(Notification.EXTRA_TITLE);
            CharSequence contentCS = notification.extras.getCharSequence(Notification.EXTRA_TEXT);

            String title = titleCS != null ? titleCS.toString() : null;
            String content = contentCS != null ? contentCS.toString() : null;
            viewHolder.noti_title.setText(title);
            viewHolder.noti_content.setText(content);
            viewHolder.noti_ic.setImageDrawable(AppUtils.getAppIcon(statusBarNotification.getPackageName()));
        }
        return view;
    }

    private static class ViewHolder {
        public ImageView noti_ic;
        public TextView noti_title;
        public TextView noti_content;
    }
}