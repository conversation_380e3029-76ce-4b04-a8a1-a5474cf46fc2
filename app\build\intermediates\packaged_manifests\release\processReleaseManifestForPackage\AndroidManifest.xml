<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.yk.secondary.screen.settings"
    android:sharedUserId="android.uid.system"
    android:versionCode="6"
    android:versionName="1.6.0" >

    <uses-sdk
        android:minSdkVersion="34"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="com.google.android.deskclock.permission.RECEIVE_ALERT_BROADCASTS" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND" />
    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission
        android:name="android.permission.WRITE_SMS"
        android:protectionLevel="signature|privileged" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_MMS" />
    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
    <uses-permission android:name="android.permission.ACCESS_MESSAGES_ON_ICC" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />

    <uses-feature android:name="android.hardware.camera" />

    <permission
        android:name="com.yk.secondary.screen.settings.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.yk.secondary.screen.settings.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.yk.secondary.screen.settings.App"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.SecondaryScreenSettings" >
        <activity
            android:name="com.yk.secondary.screen.settings.ui.SettingsActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.INFO" />
            </intent-filter>
            <intent-filter android:priority="5" >
                <action android:name="com.android.settings.action.EXTRA_SETTINGS" />
            </intent-filter>

            <meta-data
                android:name="com.android.settings.category"
                android:value="com.android.settings.category.ia.homepage" />
            <meta-data
                android:name="com.android.settings.icon"
                android:resource="@drawable/ic_settings_subscreen" />
            <!-- 菜单标题 -->
            <meta-data
                android:name="com.android.settings.title"
                android:value="@string/app_name" />
        </activity>
        <activity
            android:name="com.yk.secondary.screen.settings.ui.ClockSelectActivity"
            android:exported="false"
            android:label="@string/subscreen_clock_select"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings" />
        <activity
            android:name="com.yk.secondary.screen.settings.ui.MusicActivity"
            android:exported="false"
            android:label="@string/subscreen_clock_select"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings" />
        <activity
            android:name="com.yk.secondary.screen.settings.ui.NofiAppsSelectActivity"
            android:exported="false"
            android:label="@string/subscreen_show_noti_apps_title"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings" />

        <service
            android:name="com.yk.secondary.screen.settings.service.NotificationObserverService"
            android:excludeFromRecents="true"
            android:exported="false"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:stopWithTask="false" >
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>
        <service
            android:name="com.yk.secondary.screen.settings.factorytest.FactoryTestService"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.yk.aidl.factory.test" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.yk.secondary.screen.settings.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>