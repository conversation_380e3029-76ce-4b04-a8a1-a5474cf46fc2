package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BACK_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_FRONT_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_BACK_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_FRONT_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_GOHOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.MSG_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.media.MediaActionSound;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.util.Size;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.camera.core.Camera;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.CameraState;
import androidx.camera.core.ImageAnalysis;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.google.common.util.concurrent.ListenableFuture;
import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.Constant;
import com.yk.secondary.screen.settings.utils.FileUtils;
import com.yk.secondary.screen.settings.utils.ImageUtils;
import com.yk.secondary.screen.settings.utils.PathUtils;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.utils.Utils;
import com.yk.secondary.screen.settings.view.CameraFrameLayout;
import com.yk.secondary.screen.settings.view.MainHorizontalScrollView;

import java.io.File;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

public class CameraManager {

    private CameraManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final CameraManager instance = new CameraManager();
    }

    public static CameraManager getInstance() {
        return CameraManager.SingletonHolder.instance;
    }

    private final AtomicBoolean mIsShow = new AtomicBoolean(false);

    public boolean isShow() {
        return mIsShow.get();
    }

    private void setIsShow(boolean show) {
        mIsShow.set(show);
        if (show) {
            SecondaryScreenBitmapManager.getInstance().removeGoHomeTimeMsg();
        } else {
            SecondaryScreenBitmapManager.getInstance().sendGoHomeTimeMsg();
        }
    }

    private final AtomicBoolean isInitCamera = new AtomicBoolean(false);

    private FrameLayout mIconRootView;

    private CameraFrameLayout mCameraRootView;
    private ImageView mIvThumbnail;

    public CameraFrameLayout getCameraRootView() {
        return mCameraRootView;
    }

    private HandlerThread mHandlerThread;
    private Handler mCameraRequestHandler;
    protected MediaActionSound mCameraSound;
    private ExecutorService mExecutorService;
    private CameraSelector mCurCameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;

    private ImageView mPreviewView;

    private ListenableFuture<ProcessCameraProvider> mCameraProviderFuture;
    private ProcessCameraProvider mCameraProvider;
    private ImageCapture mImageCapture;
    private ImageAnalysis mImageAnalysis;
    private final AtomicBoolean isTakePicture = new AtomicBoolean(false);//是否在拍照流程

    private MainHorizontalScrollView mMainHorizontalScrollView;

    private TextView mTvTip;

    private final Handler mGoHomeTimeHandler = new Handler(Looper.getMainLooper(), msg -> {
        if (msg.what == MSG_GO_HOME_TIME) {
            Log.i(TAG_LOG, "CameraManager : mGoHomeTimeHandler MSG_GO_HOME_TIME .... ");
            PhotoManager.getInstance().onDestroy(false);
            onDestroy(false);
            //显示表盘
            SecondaryScreenBitmapManager.getInstance().showWatchView();
        }
        return false;
    });

    public void setYkHorizontalScrollView(MainHorizontalScrollView mMainHorizontalScrollView) {
        this.mMainHorizontalScrollView = mMainHorizontalScrollView;
    }

    public void setCameraRootView(CameraFrameLayout mCameraRootView) {
        this.mCameraRootView = mCameraRootView;
    }

    @SuppressLint("InflateParams")
    public View createCameraIconView() {
        Log.i(TAG_LOG, "CameraManager createCameraIconView: ");
        if (mIconRootView == null) {
            mIconRootView = (FrameLayout) SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_root_main, null);
        }

        View view = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_camera_icon, null);
        mTvTip = view.findViewById(R.id.tv_tip);
        FrameLayout fl = view.findViewById(R.id.fl_camera_icon);
        if (mMainHorizontalScrollView != null) {
            mMainHorizontalScrollView.setOnClickListener(fl, id -> {
                if (id == R.id.fl_camera_icon) {
                    if (!Utils.isDeviceProvisioned(App.getInstance().getContext())) {
                        Log.i(TAG_LOG, "CameraManager: isDeviceProvisioned ...........");
                        return;
                    }

                    launcherCameraView();
                }
            });
        }
        mIconRootView.removeAllViews();
        mIconRootView.addView(view);
        return mIconRootView;
    }

    public void showCameraView() {
        if (mCameraRootView == null) {
            return;
        }
        Log.i(TAG_LOG, "CameraManager showCameraView: ");
        sendGoHomeTimeMsg();
        setIsShow(true);
        isInitCamera.set(true);
        isTakePicture.set(false);
        @SuppressLint("InflateParams") View view = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_camera, null);
        mPreviewView = view.findViewById(R.id.preview);
        ImageView ivBack = view.findViewById(R.id.btn_back);
        mCameraRootView.setOnClickListener(ivBack, id -> {
            if (id == R.id.btn_back) {
                if (isInitCamera.get()) {
                    Log.i(TAG_LOG, "CameraManager showCameraView: 相机正在初始化 ... ");
                    return;
                }
                ThreadUtils.runOnUiThread(() -> onDestroy(false));
            }
        });
        ImageView ivShutter = view.findViewById(R.id.btn_shutter);
        mCameraRootView.setOnClickListener(ivShutter, id -> {
            if (id == R.id.btn_shutter) {
                if (isInitCamera.get()) {
                    Log.i(TAG_LOG, "CameraManager showCameraView: 相机正在初始化 ... ");
                    return;
                }

                if (isTakePicture.get()) {
                    Log.i(TAG_LOG, "showCameraView: 拍照中 .... ");
                    return;
                }

                if (mImageCapture != null) {
                    isTakePicture.set(true);
                    String parentPath = PathUtils.getExternalDcimPath() + "/MiniScreen/";
                    if (!FileUtils.isFileExists(App.getInstance().getContext(), new File(parentPath))) {
                        FileUtils.createOrExistsDir(new File(parentPath));
                    }

                    ImageCapture.OutputFileOptions outputFileOptions = new ImageCapture.OutputFileOptions.Builder(new File(parentPath + "IMG_" + TimeUtils.millis2String(System.currentTimeMillis(), TimeUtils.getSafeDateFormat("yyyyMMdd_HHmmss")) + ".jpg")).build();
                    mImageCapture.takePicture(outputFileOptions, mExecutorService, new ImageCapture.OnImageSavedCallback() {
                        @Override
                        public void onImageSaved(@NonNull ImageCapture.OutputFileResults outputFileResults) {
                            Uri uri = outputFileResults.getSavedUri();
                            if (uri == null) {
                                isTakePicture.set(false);
                                return;
                            }
                            String path = uri.getPath();
                            if (TextUtils.isEmpty(path)) {
                                isTakePicture.set(false);
                                return;
                            }
                            // insert your code here.
                            if (mCurCameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
                                playShutterSound();
                                Bitmap bitmap = ImageUtils.mirror(ImageUtils.getBitmap(path), true);
                                ThreadUtils.runOnUiThread(() -> {
                                    mIvThumbnail.setVisibility(View.VISIBLE);
                                    if (bitmap != null) {
                                        Glide.with(App.getInstance().getContext()).load(bitmap).circleCrop().into(mIvThumbnail);
                                    } else {
                                        mIvThumbnail.setImageResource(R.drawable.ic_thumbnail);
                                    }
                                    isTakePicture.set(false);
                                });
                                ImageUtils.save(bitmap, path, Bitmap.CompressFormat.JPEG);
                            } else {
                                playShutterSound();
                                getThumbnailFromProvider();
                                isTakePicture.set(false);
                            }

                            Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                            intent.setData(uri);
                            App.getInstance().getContext().sendBroadcast(intent);
                        }

                        @Override
                        public void onError(@NonNull ImageCaptureException error) {
                            // insert your code here.
                            isTakePicture.set(false);
                            Log.i(TAG_LOG, "CameraManager takePicture onError: " + error.getMessage());
                        }
                    });
                }
            }
        });
        mIvThumbnail = view.findViewById(R.id.iv_camera_thumbnail);
        mCameraRootView.setOnClickListener(mIvThumbnail, id -> {
            if (id == R.id.iv_camera_thumbnail) {
                if (isInitCamera.get()) {
                    Log.i(TAG_LOG, "CameraManager showCameraView: 相机正在初始化 ... ");
                    return;
                }

                if (isTakePicture.get()) {
                    Log.i(TAG_LOG, "showCameraView: 拍照中 .... ");
                    return;
                }

                //判断目录下是否有照片
                String parentPath = PathUtils.getExternalDcimPath() + "/MiniScreen/";
                File[] list = new File(parentPath).listFiles();
                if (list == null || list.length == 0) {
                    Log.i(TAG_LOG, "CameraManager showCameraView268 : 没有照片 ... ");
                    return;
                }
                boolean isCompliance = false;
                for (File file : list) {
                    if (file.getName().startsWith("IMG_")) {
                        isCompliance = true;
                        break;
                    }
                }
                if (isCompliance) {
                    ThreadUtils.runOnUiThread(() -> {
                        isTakePicture.set(false);
                        PhotoManager.getInstance().showPhotoView();
                        onDestroy(true);
                    });
                } else {
                    Log.i(TAG_LOG, "CameraManager showCameraView 286 : 没有照片 ... ");
                }
            }
        });
        getThumbnailFromProvider();

        ImageView ivCameraSwitch = view.findViewById(R.id.iv_camera_switch);
        mCameraRootView.setOnClickListener(ivCameraSwitch, id -> {
            if (id == R.id.iv_camera_switch) {
                if (isInitCamera.get()) {
                    Log.i(TAG_LOG, "CameraManager showCameraView: 相机正在初始化 ... ");
                    return;
                }
                ThreadUtils.runOnUiThread(this::switchCamera);
            }
        });
        ivShutter.setEnabled(false);
        ivCameraSwitch.setEnabled(false);
        mCameraRootView.removeAllViews();
        mCameraRootView.addView(view);
        mCameraRootView.setVisibility(View.VISIBLE);
        initData();
        startCamera(mCurCameraSelector);
    }

    private void initData() {
        Log.i(TAG_LOG, "CameraManager initData: ");
        mHandlerThread = new HandlerThread("Secondary Camera2 Task");
        mHandlerThread.start();
        mCameraRequestHandler = new Handler(mHandlerThread.getLooper());
        // 请求 CameraProvider
        mCameraProviderFuture = ProcessCameraProvider.getInstance(App.getInstance().getContext());
        initCameraSound();
        mExecutorService = Executors.newSingleThreadExecutor();
        mImageAnalysis = new ImageAnalysis.Builder().setTargetResolution(new Size(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT)).setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST).build();
        mImageAnalysis.setAnalyzer(ContextCompat.getMainExecutor(App.getInstance().getContext()), image -> {
            if (isInitCamera.get()) {
                isInitCamera.set(false);
            }

            if (mPreviewView != null) {
                if (mCurCameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
                    mPreviewView.setImageBitmap(ImageUtils.rotateAndMirror(image.toBitmap(), 90, 0, 0, true));
                } else {
                    mPreviewView.setImageBitmap(ImageUtils.rotate(image.toBitmap(), -90, 0, 0, true));
                }
            }
            image.close();
        });
    }


    private void startCamera(CameraSelector cameraSelector) {
        Log.i(TAG_LOG, "CameraManager startCamera: ");
        //检查 CameraProvider 可用性，验证它能否在视图创建后成功初始化
        mCameraProviderFuture.addListener(() -> {
            try {
                mCameraProvider = mCameraProviderFuture.get();
                mCameraProvider.unbindAll();
                String value;
                if (cameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
                    value = SPUtils.getInstance().getString(KEY_BACK_CAMERA_PHOTOSIZE_PICKER, DEFAULT_BACK_CAMERA_PHOTOSIZE_PICKER);
                } else {
                    value = SPUtils.getInstance().getString(KEY_FRONT_CAMERA_PHOTOSIZE_PICKER, DEFAULT_FRONT_CAMERA_PHOTOSIZE_PICKER);
                }
                String[] strings = value.split("x");
                int width = Integer.parseInt(strings[1]);
                int height = Integer.parseInt(strings[0]);
                Size size = new Size(width, height);
                Log.i(TAG_LOG, "initData: pic size " + size);
                mImageCapture = new ImageCapture.Builder().setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                        .setFlashMode(ImageCapture.FLASH_MODE_OFF)
                        .setTargetResolution(size).build();
                Camera camera = mCameraProvider.bindToLifecycle(mCameraRootView, cameraSelector,
                        mImageAnalysis, mImageCapture);
                camera.getCameraInfo().getCameraState().observeForever(cameraState -> {

                    CameraState.StateError stateError = cameraState.getError();
                    if (cameraState.getType() == CameraState.Type.OPEN) {
                        mCurCameraSelector = cameraSelector;
                    }

                    //主界面在使用相机
                    if (stateError != null && stateError.getCode() == CameraState.ERROR_CAMERA_IN_USE) {
                        onDestroy(false);
                    }
                });
            } catch (ExecutionException | InterruptedException e) {
                Log.i(TAG_LOG, "startCamera: ExecutionException " + e.getMessage());
            }
        }, ContextCompat.getMainExecutor(App.getInstance().getContext()));
    }

    private void switchCamera() {
        Log.i(TAG_LOG, "CameraManager switchCamera: ");
        CameraSelector temp;
        if (mCurCameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
            temp = CameraSelector.DEFAULT_FRONT_CAMERA;
        } else {
            temp = CameraSelector.DEFAULT_BACK_CAMERA;
        }
        startCamera(temp);
    }

    public void onDestroy(boolean isShowCameraRootView) {
        Log.i(TAG_LOG, "CameraManager stopCamera: ");
        mCurCameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;
        if ((mCameraProvider != null)) {
            mCameraProvider.unbindAll();
        }

        if (mCameraRequestHandler != null) {
            mCameraRequestHandler.removeCallbacksAndMessages(null);
        }

        if (mHandlerThread != null) {
            mHandlerThread.quitSafely();
        }

        if (mExecutorService != null) {
            mExecutorService.shutdown();
        }

        mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        mGoHomeTimeHandler.removeCallbacksAndMessages(null);

        if (mCameraRootView != null) {
            mCameraRootView.clearClickListener();
            mCameraRootView.removeAllViews();
            if (!isShowCameraRootView) {
                mCameraRootView.setVisibility(View.GONE);
            }
            setIsShow(false);
        }
    }

    private void playShutterSound() {
        if (!Utils.isTakePhotoVoiceEnable()) {
            return;
        }
        if (mCameraSound != null) {
            mCameraSound.play(MediaActionSound.SHUTTER_CLICK);
        }
    }

    private void initCameraSound() {
        // use SerialExecutor to sync
        if (mCameraRequestHandler != null) {
            mCameraRequestHandler.post(() -> {
                if (mCameraSound == null) {
                    try {
                        mCameraSound = new MediaActionSound();
                        // Not required, but reduces latency when playback is requested later
                        mCameraSound.load(MediaActionSound.SHUTTER_CLICK);
                    } catch (Exception e) {
                        Log.i(TAG_LOG, "CameraManager initCameraSound: " + e.getMessage());
                    }
                }
            });
        }
    }

    private boolean isCameraId0Available = false;
    private boolean isCameraId1Available = false;

    private void launcherCameraView() {
        Log.i(TAG_LOG, "CameraManager launcherCameraView: ");
        isCameraId0Available = false;
        isCameraId1Available = false;
        android.hardware.camera2.CameraManager cameraManager = (android.hardware.camera2.CameraManager) App.getInstance().getContext().getSystemService(Context.CAMERA_SERVICE);
        cameraManager.registerAvailabilityCallback(new android.hardware.camera2.CameraManager.AvailabilityCallback() {
            @Override
            public void onCameraAvailable(@NonNull String cameraId) {
                if (TextUtils.equals(cameraId, "0")) {
                    isCameraId0Available = true;
                }

                if (TextUtils.equals(cameraId, "1")) {
                    isCameraId1Available = true;
                }

                String curPackageName = App.getInstance().getCurPackageName();
                Log.i(TAG_LOG, "CameraManager onCameraAvailable: curPackageName " + curPackageName);
                if (!TextUtils.equals(curPackageName, "com.android.camera2") && isCameraId0Available && isCameraId1Available) {
                    cameraManager.unregisterAvailabilityCallback(this);
                    //启动相机拍照页面
                    showCameraView();
                }
            }

            @Override
            public void onCameraUnavailable(@NonNull String cameraId) {
                if (mTvTip != null && mTvTip.getVisibility() == View.GONE) {
                    mTvTip.setVisibility(View.VISIBLE);
                    ThreadUtils.runOnUiThreadDelayed(() -> mTvTip.setVisibility(View.GONE), 2000);
                }
                cameraManager.unregisterAvailabilityCallback(this);
            }
        }, new Handler(Looper.getMainLooper()));
    }

    public void onIconViewDestroy() {
        if (mIconRootView != null) {
            mIconRootView.removeAllViews();
            mIconRootView = null;
        }
    }

    public void sendGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
        int time = SPUtils.getInstance().getInt(KEY_SUBSCREEN_GOHOME_TIME, DEFAULT_GO_HOME_TIME);
        if (time != -1) {
            mGoHomeTimeHandler.sendEmptyMessageDelayed(MSG_GO_HOME_TIME, time);
        }
    }

    public void removeGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
    }

    private void getThumbnailFromProvider() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Uri>() {
            @Override
            public Uri doInBackground() {
                Uri mUri = null;
                String queryPath = MediaStore.Images.Media.RELATIVE_PATH;
                String sortOrder = MediaStore.Images.Media._ID + " DESC";
                String selection = queryPath + "=? ";
                Bundle queryBundle = new Bundle();
                queryBundle.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, selection);
                queryBundle.putStringArray(ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS, new String[]{"DCIM/MiniScreen/"});
                queryBundle.putString(ContentResolver.QUERY_ARG_SQL_SORT_ORDER, sortOrder);
                queryBundle.putInt(ContentResolver.QUERY_ARG_LIMIT, 1);
                Cursor cursor = App.getInstance().getContext().getContentResolver().query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, new String[]{MediaStore.Files.FileColumns._ID}, queryBundle, null);

                if (cursor != null && cursor.moveToNext()) {
                    do {
                        //查出id
                        @SuppressLint("Range") int id = cursor.getInt(cursor.getColumnIndex(MediaStore.Images.Media._ID));
                        //根据id查询URI
                        mUri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);
                    } while (cursor.moveToNext());
                }
                //关闭查询
                if (cursor != null) {
                    cursor.close();
                }
                return mUri;
            }

            @Override
            public void onSuccess(Uri uri) {
                if (mIvThumbnail == null) {
                    return;
                }

                mIvThumbnail.setVisibility(View.VISIBLE);
                if (uri != null) {
                    Glide.with(App.getInstance().getContext()).load(uri).circleCrop().into(mIvThumbnail);
                } else {
                    mIvThumbnail.setImageResource(R.drawable.ic_thumbnail);
                }
            }
        });

    }
}
