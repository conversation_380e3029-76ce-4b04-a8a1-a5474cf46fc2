#include <cstdio>
#include <cstdlib>
#include <unistd.h>
#include <pthread.h>
#include <cstring>
#include <jni.h>
#include <android/log.h>
#include <cstdint>
#include <dirent.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/inotify.h>
#include <sys/limits.h>
#include <sys/poll.h>
#include <linux/input.h>
#include <cerrno>
#include <sys/epoll.h>
#include "debugLog.h"

extern "C"
{
//驱动路径
#define TOUCH_PANEL_DEVICE_NAME "hyn_ts"


//全局变量
JavaVM *g_jvm = NULL;
JNIEnv *g_env = NULL;
jobject g_obj = NULL;
jclass native_clazz;
jmethodID callback;

static struct epoll_event input_event;
static int epollfd = 0;

int ev_init(void) {
    DIR *dir;
    struct dirent *de;
    int fd = 0, ret = 0;
    char name[10] = {0};

    dir = opendir("/dev/input");
    if (dir == NULL) {
        LOGE("open input dir failed !!\n");
        return -1;
    }
    epollfd = epoll_create(1);
    if (epollfd < 0) {
        LOGE("epoll_create failed: %s", strerror(errno));
        closedir(dir);
        return epollfd;
    }
    while ((de = readdir(dir))) {
        if (strncmp(de->d_name, "event", 5))
            continue;
        fd = openat(dirfd(dir), de->d_name, O_RDONLY);
        if (fd < 0) {
            continue;
        }
        ioctl(fd, EVIOCGNAME(sizeof(name) - 1), name);
//        LOGE("get %s, need %s\n", name, TOUCH_PANEL_DEVICE_NAME);
        if (!strcmp(name, TOUCH_PANEL_DEVICE_NAME)) {
            input_event.events = EPOLLIN | EPOLLWAKEUP;
            input_event.data.fd = fd;
            ret = epoll_ctl(epollfd, EPOLL_CTL_ADD, fd, &input_event);
            if (ret < 0) {
                LOGE("add_to_epoll: %s failed (%s)", de->d_name, strerror(errno));
            }
            break;
        }
    }
    closedir(dir);
    return 0;
}

void ev_exit(void) {
    int fd;

    fd = input_event.data.fd;
    epoll_ctl(epollfd, EPOLL_CTL_DEL, fd, NULL);
    close(fd);
    close(epollfd);
    LOGI("ev_exit done!");
}

/* wait: 0 dont wait; -1 wait forever; >0 wait ms */
int ev_get(struct input_event *ev, int wait_ms) {
    struct epoll_event mPendingEventItem;
    int pollres = 0, res = 0;
    unsigned n;

    pollres = epoll_wait(epollfd, &mPendingEventItem, 1, wait_ms);
    if (pollres == 0) {
        return -1;
    }

    if (pollres < 0) {
        LOGE("epollfd failed errno=%d(%s)\n", errno, strerror(errno));
        return -1;
    }
    res = read(mPendingEventItem.data.fd, ev, sizeof(*ev));
//    LOGI("ev get res =%d  %s", res, strerror(errno));
    if (res > 0 && (ev->type == EV_ABS || ev->type == EV_KEY || ev->type == EV_SYN)) {
//        LOGI("get event(type:0x%02x, code:0x%02x, value:0x%02x)\n", ev->type, ev->code, ev->value);
        return 0;
    }

    return -1;
}


JNIEXPORT void JNICALL
Java_com_yk_secondary_screen_settings_App_startSecondaryScreenEvent(JNIEnv *env, jobject thiz) {
    struct input_event ev;
    struct timeval down_time;
    int last_x = -1, last_y = -1, last_action = -1;
    int ret;
    LOGI("jni startSecondaryScreenEvent");

    //保存全局JVM以便在子线程中使用
    (*env).GetJavaVM(&g_jvm);
    //不能直接赋值(g_obj = obj)
    g_obj = (*env).NewGlobalRef(thiz);
    g_env = env;
    native_clazz = (*g_env).GetObjectClass(g_obj);
    callback = (*g_env).GetMethodID(native_clazz, "onKeyEvent", "(IIIJJ)V"); //参数为3个int，返回值为void
    ret = ev_init();
    if (ret < 0) {
        LOGE("ev_init failed!\n");
        return;
    }
    while (1) {
        //实现读取数据方法，赋值
        ret = ev_get(&ev, -1);
        if (ret < 0) {
            continue;
        }
        if (ev.type == EV_SYN) {
//            LOGI("report abs event(x:%d, y:%d, action:%d)\n", last_x, last_y, last_action);
            (*g_env).CallVoidMethod(g_obj, callback, last_action, last_x, last_y, down_time.tv_sec * 1000 + down_time.tv_usec / 1000/* downTime*/,
                                    ev.time.tv_sec * 1000 + ev.time.tv_usec / 1000/* eventTime */);
        } else if (ev.type == EV_KEY) {
            if (ev.code == BTN_TOUCH) {
                last_action = !ev.value;
                if (ev.value == 1) {
                    down_time.tv_sec = ev.time.tv_sec;
                    down_time.tv_usec = ev.time.tv_usec;
                }
            }
        } else if (ev.type == EV_ABS) {
            if (ev.code == ABS_MT_POSITION_X) {
                last_x = ev.value;
            } else if (ev.code == ABS_MT_POSITION_Y) {
                last_y = ev.value;
            }
        }
    }
}


JNIEXPORT void JNICALL
Java_com_yk_secondary_screen_settings_App_stopSecondaryScreenEvent(JNIEnv *env, jobject thiz) {
    LOGI("jni stopSecondaryScreenEvent");
    ev_exit();
}
}
