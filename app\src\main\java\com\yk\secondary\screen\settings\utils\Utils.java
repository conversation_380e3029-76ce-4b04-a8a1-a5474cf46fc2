package com.yk.secondary.screen.settings.utils;

import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_AOD_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BRIGHT_SCREEN_MODE_DOUBLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BRIGHT_SCREEN_MODE_FLIP;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BRIGHT_SCREEN_MODE_HOME;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_MATER_SWITCH;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_MUSIC_PACKAGE_NAME;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_OPEN_MUSIC_APP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_SUBSCREEN_CALL_TIP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_SUBSCREEN_NOTI_TIP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_SUBSCREEN_SELECTED_NOTI_APPS;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_DOUBLE_TOUCH_KEY;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_MINISCREEN_MAIN_SWITCH;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_OPEN_MUSIC_APP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_PICKED_MUSIC_APP;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_AOD_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CALL_TIP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_FLIP_SCREENON;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_NOTI_TIP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_SELECTED_NOTI_APPS;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_WITH_MAINSCREEN;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.Context;
import android.content.res.Configuration;
import android.database.Cursor;
import android.net.Uri;
import android.provider.ContactsContract;
import android.provider.Settings;

import java.util.Calendar;
import java.util.Locale;

public class Utils {

    /**
     * 副屏总开关是否打开
     */
    public static boolean isOpenSecondaryScreen() {
        return SPUtils.getInstance().getBoolean(KEY_MINISCREEN_MAIN_SWITCH, DEFAULT_MATER_SWITCH);
    }

    /**
     * 设置副屏总开关
     */
    public static void setSecondaryScreenState(boolean isOpen) {
        SPUtils.getInstance().put(KEY_MINISCREEN_MAIN_SWITCH, isOpen);
    }

    /**
     * 是否息屏显示
     */
    public static boolean isAodEnable() {
        return SPUtils.getInstance().getBoolean(KEY_SUBSCREEN_AOD_ENABLE, DEFAULT_AOD_ENABLE);
    }

    /**
     * 设置是否息屏显示
     */
    public static void setAodEnable(boolean isOpen) {
        SPUtils.getInstance().put(KEY_SUBSCREEN_AOD_ENABLE, isOpen);
    }

    /**
     * 是否随主屏亮屏
     */
    public static boolean isWithMainScreen() {
        return SPUtils.getInstance().getBoolean(KEY_SUBSCREEN_WITH_MAINSCREEN, DEFAULT_BRIGHT_SCREEN_MODE_HOME);
    }

    /**
     * 设置是否随主屏亮屏
     */
    public static void setWithMainScreen(boolean isOpen) {
        SPUtils.getInstance().put(KEY_SUBSCREEN_WITH_MAINSCREEN, isOpen);
    }

    /**
     * 是否翻转亮屏
     */
    public static boolean isFlipScreenOn() {
        return SPUtils.getInstance().getBoolean(KEY_SUBSCREEN_FLIP_SCREENON, DEFAULT_BRIGHT_SCREEN_MODE_FLIP);
    }

    /**
     * 设置是否翻转亮屏
     */
    public static void setFlipScreenOn(boolean isOpen) {
        SPUtils.getInstance().put(KEY_SUBSCREEN_FLIP_SCREENON, isOpen);
    }

    /**
     * 是否双击亮屏
     */
    public static boolean isDoubleTouchKeyScreenOn() {
        return SPUtils.getInstance().getBoolean(KEY_DOUBLE_TOUCH_KEY, DEFAULT_BRIGHT_SCREEN_MODE_DOUBLE);
    }

    /**
     * 设置是否双击亮屏
     */
    public static void setDoubleTouchKeyScreenOn(boolean isOpen) {
        SPUtils.getInstance().put(KEY_DOUBLE_TOUCH_KEY, isOpen);
    }

    /**
     * 是否通话提示
     */
    public static boolean isCallTipEnable() {
        return SPUtils.getInstance().getBoolean(KEY_SUBSCREEN_CALL_TIP_ENABLE, DEFAULT_SUBSCREEN_CALL_TIP_ENABLE);
    }

    /**
     * 设置是否通话提示
     */
    public static void setCallTipEnable(boolean isOpen) {
        SPUtils.getInstance().put(KEY_SUBSCREEN_CALL_TIP_ENABLE, isOpen);
    }

    /**
     * 是否通知提示
     */
    public static boolean isNotiTipEnable() {
        return SPUtils.getInstance().getBoolean(KEY_SUBSCREEN_NOTI_TIP_ENABLE, DEFAULT_SUBSCREEN_NOTI_TIP_ENABLE);
    }

    /**
     * 设置是否通知提示
     */
    public static void setNotiTipEnable(boolean isOpen) {
        SPUtils.getInstance().put(KEY_SUBSCREEN_NOTI_TIP_ENABLE, isOpen);
    }

    /**
     * 是否打开音乐应用
     */
    public static boolean isOpenMusicApp() {
        return SPUtils.getInstance().getBoolean(KEY_OPEN_MUSIC_APP_ENABLE, DEFAULT_OPEN_MUSIC_APP_ENABLE);
    }

    /**
     * 设置是否打开音乐应用
     */
    public static void setOpenMusicApp(boolean isOpen) {
        SPUtils.getInstance().put(KEY_OPEN_MUSIC_APP_ENABLE, isOpen);
    }

    /**
     * 是否打开拍照声音
     */
    public static boolean isTakePhotoVoiceEnable() {
        return SPUtils.getInstance().getBoolean(KEY_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE, DEFAULT_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE);
    }

    /**
     * 设置是否打开拍照声音
     */
    public static void setTakePhotoVoiceEnable(boolean isOpen) {
        SPUtils.getInstance().put(KEY_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE, isOpen);
    }

    public static boolean isDeviceProvisioned(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), Settings.Global.DEVICE_PROVISIONED, 0) == 1;
    }

    /**
     * 获取副屏音乐界面播放按钮打开的应用包名
     */
    public static String getPickedMusicAppPkgName() {
        return SPUtils.getInstance().getString(KEY_PICKED_MUSIC_APP, DEFAULT_MUSIC_PACKAGE_NAME);
    }

    /**
     * 设置副屏音乐界面播放按钮打开的应用包名
     */
    public static void setPickedMusicAppPkgName(String pkgName) {
        SPUtils.getInstance().put(KEY_PICKED_MUSIC_APP, pkgName);
    }

    public static String getSubScreenSelectedNotiApps() {
        return SPUtils.getInstance().getString(KEY_SUBSCREEN_SELECTED_NOTI_APPS, DEFAULT_SUBSCREEN_SELECTED_NOTI_APPS);
    }

    public static void setSubScreenSelectedNotiApps(String str) {
        SPUtils.getInstance().put(KEY_SUBSCREEN_SELECTED_NOTI_APPS, str);
    }

    public static String getContactNameByNumber(ContentResolver contentResolver, String number) {
        Uri uri = Uri.withAppendedPath(ContactsContract.CommonDataKinds.Phone.CONTENT_FILTER_URI, number);
        Cursor cursor = contentResolver.query(uri, new String[]{
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                ContactsContract.CommonDataKinds.Phone.NUMBER
        }, null, null, null);

        if (cursor != null) {
            if (cursor.moveToFirst()) {
                @SuppressLint("Range") String name = cursor.getString(cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME));
                cursor.close();
                return name;
            }
            cursor.close();
        }
        return "";
    }

    public static boolean isChinese(Context context) {
        Configuration config = context.getResources().getConfiguration();
        Locale locale = config.getLocales().get(0); // 获取第一个locale对象，通常是首选语言/地区设置
        return "zh".equals(locale.getLanguage()); // 检查是否是中文语言代码
    }

    public static boolean isAm() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        return hour < 12;
    }
}
