package com.yk.secondary.screen.settings.adapter;


import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.TextView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.AppUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.ArrayList;
import java.util.List;

public class MusicAppSelectAdapter extends BaseAdapter {

    private final ArrayList<AppUtils.AppInfo> mAppInfoList = new ArrayList<>();

    public void addAppInfoList(List<AppUtils.AppInfo> list) {
        mAppInfoList.clear();
        mAppInfoList.addAll(list);
    }

    @Override
    public int getCount() {
        return mAppInfoList.size();
    }

    @Override
    public AppUtils.AppInfo getItem(int i) {
        return this.mAppInfoList.get(i);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        View view;
        MusicAppSelectAdapter.ViewHolder viewHolder;
        if (convertView == null) {
            viewHolder = new MusicAppSelectAdapter.ViewHolder();
            view = LayoutInflater.from(App.getInstance().getContext()).inflate(R.layout.item_app_select_view, (ViewGroup) null);
            viewHolder.icon_app_select = view.findViewById(R.id.icon_app_select);
            viewHolder.tv_item_app_label = view.findViewById(R.id.tv_item_app_label);
            viewHolder.item_RadioButton = view.findViewById(R.id.smart_key_state);
            view.setTag(viewHolder);
        } else {
            view = convertView;
            viewHolder = (MusicAppSelectAdapter.ViewHolder) convertView.getTag();
        }

        AppUtils.AppInfo appInfo = mAppInfoList.get(position);
        if (appInfo != null) {
            Drawable drawable = appInfo.getIcon();
            if (drawable != null) {
                viewHolder.icon_app_select.setImageDrawable(drawable);
            }
            viewHolder.tv_item_app_label.setText(appInfo.getName());

            viewHolder.item_RadioButton.setOnCheckedChangeListener(null);
            viewHolder.item_RadioButton.setChecked(TextUtils.equals(Utils.getPickedMusicAppPkgName(), appInfo.getPackageName()));

        }
        return view;
    }

    public static class ViewHolder {
        public ImageView icon_app_select;
        public TextView tv_item_app_label;
        public RadioButton item_RadioButton;
    }
}