package com.yk.secondary.screen.settings.view;

import static android.view.MotionEvent.ACTION_CANCEL;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.manager.CameraManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;
import com.yk.secondary.screen.settings.utils.Constant;

import java.util.ArrayList;

public class PhotoHorizontalScrollView extends HorizontalScrollView {

    private final ArrayList<Uri> mImgUriList = new ArrayList<>();

    private TextView mTvTag;

    private LinearLayout mChildGroup = null;

    private int mStartPos;

    private int mCurPage = 1;

    public void setChildGroup(LinearLayout mChildGroup) {
        this.mChildGroup = mChildGroup;
    }

    public void setTvTag(TextView mTvTag) {
        this.mTvTag = mTvTag;
    }

    public PhotoHorizontalScrollView(Context mContext, AttributeSet attrs, int defStyle) {
        super(mContext, attrs, defStyle);
    }

    public PhotoHorizontalScrollView(Context mContext, AttributeSet attrs) {
        super(mContext, attrs);
    }

    public PhotoHorizontalScrollView(Context mContext) {
        super(mContext);
    }

    public void setImgUriList(ArrayList<Uri> mImgUriList) {
        this.mImgUriList.clear();
        this.mImgUriList.addAll(mImgUriList);
    }

    public void initImageView() {
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
        try {
            for (Uri uri : mImgUriList) {
                ImageView imageView = new ImageView(App.getInstance().getContext());
                imageView.setScaleType(ImageView.ScaleType.FIT_XY);
                imageView.setLayoutParams(layoutParams);
                Glide.with(App.getInstance().getContext()).load(uri).listener(new RequestListener<Drawable>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                        Log.i(TAG_LOG, "PhotoHorizontalScrollView onLoadFailed: ");
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                        Log.i(TAG_LOG, "PhotoHorizontalScrollView onResourceReady: ");
                        return false;
                    }
                }).into(imageView);
                mChildGroup.addView(imageView);
            }
            scrollToPage(mCurPage - 1);
        } catch (Exception e) {
            Log.i(TAG_LOG, "PhotoHorizontalScrollView initImageView: Exception " + e.getMessage());
        }
    }

    @SuppressLint("SetTextI18n")
    private void scrollToPage(int index) {
        if (mChildGroup == null) return;
        if (index < 0 || index >= mChildGroup.getChildCount()) return;
        smoothScrollTo(getChildLeft(index), 0);
        if (mTvTag != null) {
            mTvTag.setText((index + 1) + "/" + mImgUriList.size());
        }
    }

    @SuppressLint("SetTextI18n")
    public synchronized void prevPage() {
        if (mCurPage <= 1) {
            return;
        }
        mCurPage--;
        scrollToPage(mCurPage - 1);
        if (mTvTag != null) {
            mTvTag.setText(mCurPage + "/" + mImgUriList.size());
        }
    }

    @SuppressLint("SetTextI18n")
    public synchronized void nextPage() {
        if (mCurPage >= mImgUriList.size()) {
            return;
        }
        mCurPage++;
        scrollToPage(mCurPage - 1);
        if (mTvTag != null) {
            mTvTag.setText(mCurPage + "/" + mImgUriList.size());
        }
    }

    private int getChildLeft(int index) {
        if (index >= 0 && mChildGroup != null) {
            if (index < mChildGroup.getChildCount())
                return mChildGroup.getChildAt(index).getLeft();
        }
        return 0;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                Log.i(TAG_LOG, "onTouchEvent: PhotoHorizontalScrollView");
                SecondaryScreenControlManager.getInstance().removeSecondaryScreenSleepMsg();
                CameraManager.getInstance().removeGoHomeTimeMsg();
                mStartPos = (int) ev.getX();
                break;
            case MotionEvent.ACTION_UP:
            case ACTION_CANCEL: {
                SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
                CameraManager.getInstance().sendGoHomeTimeMsg();
                if (Math.abs((ev.getX() - mStartPos)) > (float) Constant.SCREEN_WIDTH / 12) {
                    if (ev.getX() - mStartPos > 0) {
                        prevPage();
                    } else {
                        nextPage();
                    }
                } else {
                    scrollToPage(mCurPage - 1);
                }
                return true;
            }
        }
        return super.onTouchEvent(ev);
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "PhotoHorizontalScrollView onDestroy: ");
        mImgUriList.clear();
        removeAllViews();
    }
}