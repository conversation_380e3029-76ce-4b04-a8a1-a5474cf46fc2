package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_WATCH_SELECTION;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CLOCK_SELECT;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.utils.Utils;
import com.yk.secondary.screen.settings.view.watch.BaseWatchStyle;
import com.yk.secondary.screen.settings.view.watch.WatchStyle1;
import com.yk.secondary.screen.settings.view.watch.WatchStyle10;
import com.yk.secondary.screen.settings.view.watch.WatchStyle2;
import com.yk.secondary.screen.settings.view.watch.WatchStyle3;
import com.yk.secondary.screen.settings.view.watch.WatchStyle4;
import com.yk.secondary.screen.settings.view.watch.WatchStyle5;
import com.yk.secondary.screen.settings.view.watch.WatchStyle6;
import com.yk.secondary.screen.settings.view.watch.WatchStyle7;
import com.yk.secondary.screen.settings.view.watch.WatchStyle8;
import com.yk.secondary.screen.settings.view.watch.WatchStyle9;

public class WatchViewManager {
    private WatchViewManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final WatchViewManager instance = new WatchViewManager();
    }

    public static WatchViewManager getInstance() {
        return WatchViewManager.SingletonHolder.instance;
    }

    public final int WATCH_TOTAL_COUNT = 10;

    private FrameLayout mRootView;

    private BaseWatchStyle mCurWatchStyle;

    private ImageView mIvNotiTip;

    @SuppressLint("InflateParams")
    public View createWatchView() {
        Log.i(TAG_LOG, "WatchViewManager createWatchView: ");
        if (mRootView == null) {
            mRootView = (FrameLayout) SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_root_main, null);
        }
        //设置默认表盘
        mCurWatchStyle = getCurWatchStyle();
        if (mCurWatchStyle.getRootView().getParent() != null) {
            ((ViewGroup) mCurWatchStyle.getRootView().getParent()).removeView(mCurWatchStyle.getRootView());
        }
        mRootView.removeAllViews();
        mRootView.addView(mCurWatchStyle.getRootView());
        //通知提醒红点
        mIvNotiTip = new ImageView(App.getInstance().getContext());
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = 80;
        layoutParams.leftMargin = 180;
        mIvNotiTip.setLayoutParams(layoutParams);
        if (Utils.isNotiTipEnable()) {
            if (NotificationViewManager.getInstance().getStatusBarNotificationList().isEmpty()) {
                mIvNotiTip.setVisibility(View.GONE);
            } else {
                mIvNotiTip.setVisibility(View.VISIBLE);
            }
        } else {
            mIvNotiTip.setVisibility(View.GONE);
        }
        mIvNotiTip.setImageResource(R.drawable.shape_circle);
        mRootView.addView(mIvNotiTip);
        return mRootView;
    }

    public void updateNotificationTipImageView() {
        if (mRootView == null) {
            return;
        }
        if (Utils.isNotiTipEnable()) {
            if (NotificationViewManager.getInstance().getStatusBarNotificationList().isEmpty()) {
                mIvNotiTip.setVisibility(View.GONE);
            } else {
                mIvNotiTip.setVisibility(View.VISIBLE);
            }
        } else {
            mIvNotiTip.setVisibility(View.GONE);
        }
    }

    public void switchWatchStyle(boolean isDoubleClick) {
        if (mRootView == null) {
            return;
        }
        Log.i(TAG_LOG, "WatchViewManager switchWatchStyle: ");
        int currentIndex = SPUtils.getInstance().getInt(KEY_SUBSCREEN_CLOCK_SELECT, DEFAULT_WATCH_SELECTION);
        if (isDoubleClick) {
            currentIndex++;
            if (currentIndex < 0 || currentIndex > (WATCH_TOTAL_COUNT - 1)) {
                currentIndex = 0;
            }
            SPUtils.getInstance().put(KEY_SUBSCREEN_CLOCK_SELECT, currentIndex);
        }
        //先销毁上一个的
        if (mCurWatchStyle != null) {
            mCurWatchStyle.onDestroy();
        }
        mCurWatchStyle = getCurWatchStyle();
        mRootView.removeAllViews();
        mRootView.addView(mCurWatchStyle.getRootView());
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "WatchViewManager onDestroy: ");
        if (mRootView != null) {
            mRootView.removeAllViews();
            mRootView = null;
        }

        if (mCurWatchStyle != null) {
            mCurWatchStyle.onDestroy();
            mCurWatchStyle = null;
        }
    }

    private BaseWatchStyle getCurWatchStyle() {
        int index = SPUtils.getInstance().getInt(KEY_SUBSCREEN_CLOCK_SELECT, DEFAULT_WATCH_SELECTION);
        BaseWatchStyle baseWatchStyle;
        switch (index) {
            case 0:
                baseWatchStyle = new WatchStyle1();
                break;
            case 1:
                baseWatchStyle = new WatchStyle2();
                break;
            case 2:
                baseWatchStyle = new WatchStyle3();
                break;
            case 3:
                baseWatchStyle = new WatchStyle4();
                break;
            case 4:
                baseWatchStyle = new WatchStyle5();
                break;
            case 5:
                baseWatchStyle = new WatchStyle6();
                break;
            case 6:
                baseWatchStyle = new WatchStyle7();
                break;
            case 7:
                baseWatchStyle = new WatchStyle8();
                break;
            case 8:
                baseWatchStyle = new WatchStyle9();
                break;
            case 9:
                baseWatchStyle = new WatchStyle10();
                break;
            default:
                baseWatchStyle = new WatchStyle1();
                break;
        }
        return baseWatchStyle;
    }
}
