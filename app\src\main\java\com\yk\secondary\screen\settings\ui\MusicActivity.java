package com.yk.secondary.screen.settings.ui;

import android.annotation.SuppressLint;
import android.app.ActionBar;
import android.app.Activity;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.ListView;

import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.adapter.MusicAppSelectAdapter;
import com.yk.secondary.screen.settings.utils.AppUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.List;

public class MusicActivity extends Activity {

    private MusicAppSelectAdapter mMusicAppSelectAdapter;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        showActionbarBackButton();
        setContentView(R.layout.activity_musci_app);
        initViews();
        initData();
    }

    private void initViews() {
        ListView listView = findViewById(R.id.app_list);
        mMusicAppSelectAdapter = new MusicAppSelectAdapter();
        listView.setAdapter(mMusicAppSelectAdapter);
        listView.setOnItemClickListener((parent, view, position, id) -> {
            MusicAppSelectAdapter.ViewHolder viewHolder = (MusicAppSelectAdapter.ViewHolder) view.getTag();
            if (viewHolder.item_RadioButton.isChecked()) {
                return;
            }

            Utils.setPickedMusicAppPkgName(mMusicAppSelectAdapter.getItem(position).getPackageName());
            viewHolder.item_RadioButton.setChecked(true);
            finish();
        });
    }

    private void initData() {
        ThreadUtils.executeByCpu(new ThreadUtils.SimpleTask<List<AppUtils.AppInfo>>() {
            @SuppressLint("QueryPermissionsNeeded")
            @Override
            public List<AppUtils.AppInfo> doInBackground() {
                return AppUtils.getAllAppsInfo();
            }

            @Override
            public void onSuccess(List<AppUtils.AppInfo> result) {
                if (mMusicAppSelectAdapter != null) {
                    mMusicAppSelectAdapter.addAppInfoList(result);
                    mMusicAppSelectAdapter.notifyDataSetChanged();
                }
            }
        }, Thread.MAX_PRIORITY);
    }

    private void showActionbarBackButton() {
        ActionBar actionBar = getActionBar();
        if (actionBar != null) {
            actionBar.setTitle(R.string.subscreen_pick_music_app);
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeButtonEnabled(true);
            actionBar.setElevation(0);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            this.finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
