package com.yk.secondary.screen.settings.view;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;

import java.util.HashMap;

public class PhotoFrameLayout extends FrameLayout {

    private GestureDetector mGestureDetector;

    private PhotoHorizontalScrollView mPhotoHorizontalScrollView;

    public void setPhotoHorizontalScrollView(PhotoHorizontalScrollView mPhotoHorizontalScrollView) {
        this.mPhotoHorizontalScrollView = mPhotoHorizontalScrollView;
    }

    private final HashMap<Integer, OnClickListener> mClickViews = new HashMap<>();

    public void clearClickListener() {
        mClickViews.clear();
    }

    public PhotoFrameLayout(@NonNull Context context) {
        super(context);
        initDoubleTapListener(context);
    }

    public PhotoFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initDoubleTapListener(context);
    }

    public PhotoFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initDoubleTapListener(context);
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapUp(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "PhotoFrameLayout onSingleTapUp: ");
                try {
                    float x = e.getX();
                    float y = e.getY();
                    if (mClickViews.isEmpty()) {
                        return super.onSingleTapUp(e);
                    }
                    mClickViews.forEach((id, listener) -> {
                        if (id == R.id.iv_back) {//返回
                            if (x >= 87 && x <= 151 && y >= 0 && y <= 66) {
                                listener.onClick(id);
                            }
                        }
                    });
                }catch (Exception exception) {
                    Log.i(TAG_LOG, "PhotoFrameLayout onSingleTapUp: exception " + exception.getMessage());
                }
                return super.onSingleTapUp(e);
            }
        });
    }

    public void setOnClickListener(@NonNull View view, OnClickListener listener) {
        mClickViews.put(view.getId(), listener);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mGestureDetector != null) {
            mGestureDetector.onTouchEvent(event);
        }
        if (mPhotoHorizontalScrollView != null) {
            mPhotoHorizontalScrollView.onTouchEvent(event);
        }
        return true;
    }

    public interface OnClickListener {
        void onClick(int id);
    }
}
