<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@drawable/icon_watch_style7_bg"
    android:descendantFocusability="blocksDescendants">

    <ImageView
        android:id="@+id/iv_week"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="95px" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="85px"
        android:layout_marginTop="69px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_month_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_month_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="132px"
        android:layout_marginTop="69px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_day_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_day_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_battery"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="167px" />

    <com.yk.secondary.screen.settings.view.WatchStyle7ClockView
        android:id="@+id/ac"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_width"
        android:layout_gravity="center"
        tools:ignore="MissingConstraints" />

</FrameLayout>