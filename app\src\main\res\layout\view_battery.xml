<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:gravity="center">

    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/iv_charging"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_charging"
        android:scaleType="centerCrop" />

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:background="@drawable/ic_battery_background" />

    <ImageView
        android:id="@+id/iv_battery_level_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />

    <LinearLayout
        android:layout_width="46px"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="150px"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_battery_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_battery_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_battery_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_battery_num_tag" />
    </LinearLayout>

</FrameLayout>