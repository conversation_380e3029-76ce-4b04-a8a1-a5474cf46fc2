//
// Created by Administrator on 2024/8/27.
//

#ifndef BITMAPSECONDARYSCREEN_DEBUGLOG_H
#define BITMAPSECONDARYSCREEN_DEBUGLOG_H

#include <android/log.h>

#define LOG_TAG "secondary_screen_event"
//#define LOG_TAG "84018442"
#define ANDROID_PLATFORM

#ifdef ANDROID_PLATFORM
#define LOGD(...) ((void)__android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__))
#define LOGI(...) ((void)__android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__))
#define LOGW(...) ((void)__android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__))
#define LOGE(...) ((void)__android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__))

#else
#define LOGD(fmt, ...) printf(fmt"\n", ##__VA_ARGS__)
   #define LOGI(fmt, ...) printf(fmt"\n", ##__VA_ARGS__)
   #define LOGW(fmt, ...) printf(fmt"\n", ##__VA_ARGS__)
   #define LOGE(fmt, ...) printf(fmt"\n", ##__VA_ARGS__)
#endif

#endif //BITMAPSECONDARYSCREEN_DEBUGLOG_H
