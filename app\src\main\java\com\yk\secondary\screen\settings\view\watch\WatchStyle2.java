package com.yk.secondary.screen.settings.view.watch;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.animation.ObjectAnimator;
import android.os.CountDownTimer;
import android.util.Log;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.Calendar;

public class WatchStyle2 extends BaseWatchStyle {

    private LinearLayout mDateTimeView;

    private CountDownTimer mCountDownTimer;

    private ImageView mIvWeek, mIvMonth1, mIvMonth2, mIvDay1, mIvDay2, mIvTimeH1, mIvTimeH2, mIvTimeM1, mIvTimeM2;

    public WatchStyle2() {
        super();
    }

    @Override
    public void initViews() {
        mRootView = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_bitmap_watch_style2, null);
        mDateTimeView = mRootView.findViewById(R.id.ll_date_time);
        //startDateTimeAnimation();

        mIvTimeH1 = mRootView.findViewById(R.id.iv_time_h_1);
        mIvTimeH2 = mRootView.findViewById(R.id.iv_time_h_2);
        setTimeH();

        mIvTimeM1 = mRootView.findViewById(R.id.iv_time_m_1);
        mIvTimeM2 = mRootView.findViewById(R.id.iv_time_m_2);
        setTimeM();

        mIvWeek = mRootView.findViewById(R.id.iv_week);
        setWeek();

        mIvDay1 = mRootView.findViewById(R.id.iv_day_1);
        mIvDay2 = mRootView.findViewById(R.id.iv_day_2);
        setDay();

        mIvMonth1 = mRootView.findViewById(R.id.iv_month_1);
        mIvMonth2 = mRootView.findViewById(R.id.iv_month_2);
        setMonth();
    }

    @Override
    public int getStyleValue() {
        return 2;
    }

    @Override
    public void updateTimeChanged() {
        setTimeH();
        setTimeM();
        setWeek();
    }

    @Override
    public void updateDateChanged() {
        setTimeH();
        setTimeM();
        setWeek();
    }

    @Override
    public void updateCurrentLevelChanged(BatteryState batteryState) {

    }

    private void startDateTimeAnimation() {
        Log.i(TAG_LOG, "WatchStyle2 startDateTimeAnimation: ");
        if (mDateTimeView == null) {
            return;
        }
        ObjectAnimator animator = ObjectAnimator.ofFloat(mDateTimeView, "translationX", 0, -4, 0);
        animator.setDuration(250);
        animator.setStartDelay(500);
        mCountDownTimer = new CountDownTimer(Long.MAX_VALUE, 2250L) {
            @Override
            public void onTick(long millisUntilFinished) {
                Log.i(TAG_LOG, "onTick: ");
                animator.start();
            }

            @Override
            public void onFinish() {

            }
        }.start();
    }

    private void stopDateTimeAnimation() {
        Log.i(TAG_LOG, "WatchStyle2 stopDateTimeAnimation: ");
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        //stopDateTimeAnimation();
    }

    private void setDay() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("dd"));
        try {
            if (mIvDay1 != null) {
                mIvDay1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvDay2 != null) {
                mIvDay2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setDays: Exception " + e.getMessage());
        }
    }

    private void setMonth() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("MM"));
        try {
            if (mIvMonth1 != null) {
                mIvMonth1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvMonth2 != null) {
                mIvMonth2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setMonth: Exception " + e.getMessage());
        }
    }

    private void setWeek() {
        if (mIvWeek != null) {
            mIvWeek.setImageResource(getWeekImageResource());
        }
    }

    private int getDateImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style2_date_0;
            case '1':
                return R.drawable.icon_watch_style2_date_1;
            case '2':
                return R.drawable.icon_watch_style2_date_2;
            case '3':
                return R.drawable.icon_watch_style2_date_3;
            case '4':
                return R.drawable.icon_watch_style2_date_4;
            case '5':
                return R.drawable.icon_watch_style2_date_5;
            case '6':
                return R.drawable.icon_watch_style2_date_6;
            case '7':
                return R.drawable.icon_watch_style2_date_7;
            case '8':
                return R.drawable.icon_watch_style2_date_8;
            case '9':
                return R.drawable.icon_watch_style2_date_9;
            default:
                return R.drawable.icon_watch_style2_date_0;
        }
    }

    private int getWeekImageResource() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        boolean isChinese = Utils.isChinese(App.getInstance().getContext());
        switch (dayOfWeek) {
            case 1:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_sun_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_sun_en;
                }
            case 2:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_mon_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_mon_en;
                }
            case 3:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_tue_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_tue_en;
                }
            case 4:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_wed_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_wed_en;
                }
            case 5:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_thu_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_thu_en;
                }
            case 6:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_fri_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_fri_en;
                }
            case 7:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_sat_en;
                }
            default:
                if (isChinese) {
                    return R.drawable.icon_watch_style2_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style2_week_sat_en;
                }
        }
    }

    private void setTimeH() {
        String h = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH"));
        try {
            if (mIvTimeH1 != null) {
                mIvTimeH1.setImageResource(getHourOrMinImageResource(h.charAt(0)));
            }

            if (mIvTimeH2 != null) {
                mIvTimeH2.setImageResource(getHourOrMinImageResource(h.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeH: Exception " + e.getMessage());
        }
    }

    private void setTimeM() {
        String m = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("mm"));
        try {
            if (mIvTimeM1 != null) {
                mIvTimeM1.setImageResource(getHourOrMinImageResource(m.charAt(0)));
            }

            if (mIvTimeM2 != null) {
                mIvTimeM2.setImageResource(getHourOrMinImageResource(m.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeM: Exception " + e.getMessage());
        }
    }

    private int getHourOrMinImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style2_hour_min_0;
            case '1':
                return R.drawable.icon_watch_style2_hour_min_1;
            case '2':
                return R.drawable.icon_watch_style2_hour_min_2;
            case '3':
                return R.drawable.icon_watch_style2_hour_min_3;
            case '4':
                return R.drawable.icon_watch_style2_hour_min_4;
            case '5':
                return R.drawable.icon_watch_style2_hour_min_5;
            case '6':
                return R.drawable.icon_watch_style2_hour_min_6;
            case '7':
                return R.drawable.icon_watch_style2_hour_min_7;
            case '8':
                return R.drawable.icon_watch_style2_hour_min_8;
            case '9':
                return R.drawable.icon_watch_style2_hour_min_9;
            default:
                return R.drawable.icon_watch_style2_hour_min_0;
        }
    }

}
