<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_bg"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@drawable/icon_watch_style10_bg1"
    android:descendantFocusability="blocksDescendants">

    <pl.droidsonroids.gif.GifImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="160px"
        android:background="@drawable/icon_watch_style10_bg"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="90px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_month_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_month_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style10_date_tag" />

        <ImageView
            android:id="@+id/iv_day_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_day_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_week"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="150px"
        android:layout_marginTop="135px" />

    <ImageView
        android:id="@+id/iv_battery"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="54px"
        android:layout_marginTop="135px" />

    <com.yk.secondary.screen.settings.view.WatchStyle10ClockView
        android:id="@+id/ac"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_width"
        android:layout_gravity="center" />

</FrameLayout>