package com.yk.secondary.screen.settings.view.watch;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.util.Log;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.ThreadUtils;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.time.LocalTime;
import java.util.Calendar;
import java.util.concurrent.TimeUnit;

public class WatchStyle9 extends BaseWatchStyle {

    private ImageView mIvWeek, mIvDay1, mIvDay2, mIvMonth1, mIvMonth2, mIvTimeH1, mIvTimeH2, mIvTimeM1, mIvTimeM2, mIvTimeSec1, mIvTimeSec2;

    private ThreadUtils.Task<Void> mSecondTask;

    public WatchStyle9() {
        super();
    }

    @Override
    public void initViews() {
        mRootView = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_bitmap_watch_style9, null);

        mIvWeek = mRootView.findViewById(R.id.iv_week);
        setWeek();

        mIvDay1 = mRootView.findViewById(R.id.iv_day_1);
        mIvDay2 = mRootView.findViewById(R.id.iv_day_2);
        setDay();

        mIvMonth1 = mRootView.findViewById(R.id.iv_month_1);
        mIvMonth2 = mRootView.findViewById(R.id.iv_month_2);
        setMonth();

        mIvTimeH1 = mRootView.findViewById(R.id.iv_time_h_1);
        mIvTimeH2 = mRootView.findViewById(R.id.iv_time_h_2);
        setTimeH();

        mIvTimeM1 = mRootView.findViewById(R.id.iv_time_m_1);
        mIvTimeM2 = mRootView.findViewById(R.id.iv_time_m_2);
        setTimeM();

        mIvTimeSec1 = mRootView.findViewById(R.id.iv_time_sec_1);
        mIvTimeSec2 = mRootView.findViewById(R.id.iv_time_sec_2);
        setTimeSec(getCurrentSec());
        startSecondTask();
    }

    @Override
    public int getStyleValue() {
        return 9;
    }

    @Override
    public void updateTimeChanged() {
        setDay();
        setMonth();
        setTimeH();
        setTimeM();
        setWeek();
    }

    @Override
    public void updateDateChanged() {
        setDay();
        setMonth();
        setTimeH();
        setTimeM();
        setWeek();
    }

    @Override
    public void updateCurrentLevelChanged(BatteryState batteryState) {
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        stopSecondTask();
    }

    private void startSecondTask() {
        if (mSecondTask == null) {
            mSecondTask = new ThreadUtils.SimpleTask<Void>() {
                @Override
                public Void doInBackground() {
                    setTimeSec(getCurrentSec());
                    return null;
                }

                @Override
                public void onSuccess(Void result) {

                }
            };
        }
        ThreadUtils.executeByCpuAtFixRate(mSecondTask, 1, TimeUnit.SECONDS, Thread.MAX_PRIORITY);
    }

    private void stopSecondTask() {
        if (mSecondTask != null) {
            ThreadUtils.cancel(mSecondTask);
            mSecondTask = null;
        }
    }

    private String getCurrentSec() {
        LocalTime now = LocalTime.now();
        String currentSecond = String.valueOf(now.getSecond());
        if (currentSecond.length() == 1) {
            currentSecond = "0" + currentSecond;
        }
        return currentSecond;
    }

    private void setTimeH() {
        String h = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH"));
        try {
            if (mIvTimeH1 != null) {
                mIvTimeH1.setImageResource(getHourOrMinImageResource(h.charAt(0)));
            }

            if (mIvTimeH2 != null) {
                mIvTimeH2.setImageResource(getHourOrMinImageResource(h.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeH: Exception " + e.getMessage());
        }
    }

    private void setTimeM() {
        String m = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("mm"));
        try {
            if (mIvTimeM1 != null) {
                mIvTimeM1.setImageResource(getHourOrMinImageResource(m.charAt(0)));
            }

            if (mIvTimeM2 != null) {
                mIvTimeM2.setImageResource(getHourOrMinImageResource(m.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeM: Exception " + e.getMessage());
        }
    }

    private void setTimeSec(String sec) {
        try {
            if (mIvTimeSec1 != null) {
                mIvTimeSec1.setImageResource(getSecImageResource(sec.charAt(0)));
            }

            if (mIvTimeSec2 != null) {
                mIvTimeSec2.setImageResource(getSecImageResource(sec.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeM: Exception " + e.getMessage());
        }
    }

    private void setDay() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("dd"));
        try {
            if (mIvDay1 != null) {
                mIvDay1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvDay2 != null) {
                mIvDay2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setDays: Exception " + e.getMessage());
        }
    }

    private void setMonth() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("MM"));
        try {
            if (mIvMonth1 != null) {
                mIvMonth1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvMonth2 != null) {
                mIvMonth2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setMonth: Exception " + e.getMessage());
        }
    }

    private void setWeek() {
        if (mIvWeek != null) {
            mIvWeek.setImageResource(getWeekImageResource());
        }
    }

    private int getSecImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style9_sec_0;
            case '1':
                return R.drawable.icon_watch_style9_sec_1;
            case '2':
                return R.drawable.icon_watch_style9_sec_2;
            case '3':
                return R.drawable.icon_watch_style9_sec_3;
            case '4':
                return R.drawable.icon_watch_style9_sec_4;
            case '5':
                return R.drawable.icon_watch_style9_sec_5;
            case '6':
                return R.drawable.icon_watch_style9_sec_6;
            case '7':
                return R.drawable.icon_watch_style9_sec_7;
            case '8':
                return R.drawable.icon_watch_style9_sec_8;
            case '9':
                return R.drawable.icon_watch_style9_sec_9;
            default:
                return R.drawable.icon_watch_style9_sec_0;
        }
    }

    private int getHourOrMinImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style9_hour_min_0;
            case '1':
                return R.drawable.icon_watch_style9_hour_min_1;
            case '2':
                return R.drawable.icon_watch_style9_hour_min_2;
            case '3':
                return R.drawable.icon_watch_style9_hour_min_3;
            case '4':
                return R.drawable.icon_watch_style9_hour_min_4;
            case '5':
                return R.drawable.icon_watch_style9_hour_min_5;
            case '6':
                return R.drawable.icon_watch_style9_hour_min_6;
            case '7':
                return R.drawable.icon_watch_style9_hour_min_7;
            case '8':
                return R.drawable.icon_watch_style9_hour_min_8;
            case '9':
                return R.drawable.icon_watch_style9_hour_min_9;
            default:
                return R.drawable.icon_watch_style9_hour_min_0;
        }
    }

    private int getWeekImageResource() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        boolean isChinese = Utils.isChinese(App.getInstance().getContext());
        switch (dayOfWeek) {
            case 1:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_sun_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_sun_en;
                }
            case 2:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_mon_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_mon_en;
                }
            case 3:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_tue_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_tue_en;
                }
            case 4:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_wed_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_wed_en;
                }
            case 5:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_thu_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_thu_en;
                }
            case 6:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_fri_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_fri_en;
                }
            case 7:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_sat_en;
                }
            default:
                if (isChinese) {
                    return R.drawable.icon_watch_style9_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style9_week_sat_en;
                }
        }
    }

    private int getDateImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style9_date_0;
            case '1':
                return R.drawable.icon_watch_style9_date_1;
            case '2':
                return R.drawable.icon_watch_style9_date_2;
            case '3':
                return R.drawable.icon_watch_style9_date_3;
            case '4':
                return R.drawable.icon_watch_style9_date_4;
            case '5':
                return R.drawable.icon_watch_style9_date_5;
            case '6':
                return R.drawable.icon_watch_style9_date_6;
            case '7':
                return R.drawable.icon_watch_style9_date_7;
            case '8':
                return R.drawable.icon_watch_style9_date_8;
            case '9':
                return R.drawable.icon_watch_style9_date_9;
            default:
                return R.drawable.icon_watch_style9_date_0;
        }
    }

}
