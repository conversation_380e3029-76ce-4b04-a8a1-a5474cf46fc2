1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.yk.secondary.screen.settings"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="6"
6    android:versionName="1.6.0" >
7
8    <uses-sdk
9        android:minSdkVersion="34"
10        android:targetSdkVersion="34" />
11
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:5:5-81
12-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:5:22-78
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:6:5-68
13-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:6:22-65
14    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
14-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:7:5-79
14-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:7:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:8:5-81
15-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:8:22-78
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:9:5-79
16-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:9:22-76
17    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
17-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:10:5-85
17-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:10:22-82
18    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />
18-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:11:5-83
18-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:11:22-80
19    <uses-permission android:name="android.permission.INTERNET" />
19-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:12:5-67
19-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:12:22-64
20    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
20-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:13:5-76
20-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:13:22-73
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:14:5-79
21-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:14:22-76
22    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
22-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:15:5-76
22-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:15:22-73
23    <uses-permission android:name="android.permission.READ_SMS" />
23-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:16:5-67
23-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:16:22-64
24    <uses-permission android:name="android.permission.RECEIVE_SMS" />
24-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:17:5-70
24-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:17:22-67
25    <uses-permission android:name="com.google.android.deskclock.permission.RECEIVE_ALERT_BROADCASTS" />
25-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:18:5-104
25-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:18:22-101
26    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
26-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:19:5-78
26-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:19:22-75
27    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
27-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:20:5-75
27-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:20:22-72
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:21:5-77
28-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:21:22-74
29    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
29-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:22:5-77
29-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:22:22-74
30    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
30-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:23:5-75
30-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:23:22-72
31    <uses-permission android:name="android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND" />
31-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:24:5-100
31-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:24:22-97
32    <uses-permission android:name="android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND" />
32-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:25:5-118
32-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:25:22-115
33    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
33-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:26:5-89
33-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:26:22-86
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:27:5-78
34-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:27:22-75
35    <uses-permission android:name="android.permission.SEND_SMS" />
35-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:28:5-67
35-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:28:22-64
36    <uses-permission
36-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:29:5-31:58
37        android:name="android.permission.WRITE_SMS"
37-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:30:9-52
38        android:protectionLevel="signature|privileged" />
38-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:31:9-55
39    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
39-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:32:5-82
39-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:32:22-79
40    <uses-permission android:name="android.permission.RECEIVE_MMS" />
40-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:33:5-70
40-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:33:22-67
41    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
41-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:34:5-75
41-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:34:22-72
42    <uses-permission android:name="android.permission.ACCESS_MESSAGES_ON_ICC" />
42-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:35:5-81
42-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:35:22-78
43    <uses-permission android:name="android.permission.CAMERA" />
43-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:37:5-65
43-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:37:22-62
44    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
44-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:38:5-76
44-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:38:22-73
45    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
45-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:39:5-81
45-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:39:22-78
46    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
46-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:40:5-78
46-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:40:22-75
47
48    <uses-feature android:name="android.hardware.camera" />
48-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:42:5-60
48-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:42:19-57
49
50    <permission
50-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
51        android:name="com.yk.secondary.screen.settings.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.yk.secondary.screen.settings.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
54-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
55
56    <application
56-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:44:5-125:19
57        android:name="com.yk.secondary.screen.settings.App"
57-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:45:9-28
58        android:allowBackup="true"
58-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:46:9-35
59        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
59-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
60        android:dataExtractionRules="@xml/data_extraction_rules"
60-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:47:9-65
61        android:extractNativeLibs="false"
62        android:fullBackupContent="@xml/backup_rules"
62-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:48:9-54
63        android:icon="@mipmap/ic_launcher"
63-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:49:9-43
64        android:label="@string/app_name"
64-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:50:9-41
65        android:roundIcon="@mipmap/ic_launcher_round"
65-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:51:9-54
66        android:supportsRtl="true"
66-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:52:9-35
67        android:testOnly="true"
68        android:theme="@style/Theme.SecondaryScreenSettings" >
68-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:53:9-61
69        <activity
69-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:55:9-82:20
70            android:name="com.yk.secondary.screen.settings.ui.SettingsActivity"
70-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:56:13-48
71            android:exported="false"
71-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:57:13-37
72            android:label="@string/app_name"
72-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:58:13-45
73            android:launchMode="singleInstance"
73-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:59:13-48
74            android:screenOrientation="portrait"
74-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:60:13-49
75            android:theme="@style/Theme.RBSettings" >
75-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:61:13-52
76            <intent-filter>
76-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:62:13-67:29
77                <action android:name="android.intent.action.MAIN" />
77-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:63:17-69
77-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:63:25-66
78
79                <category android:name="android.intent.category.DEFAULT" />
79-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:65:17-76
79-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:65:27-73
80                <category android:name="android.intent.category.INFO" />
80-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:66:17-73
80-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:66:27-70
81            </intent-filter>
82            <intent-filter android:priority="5" >
82-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:68:13-70:29
82-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:68:28-48
83                <action android:name="com.android.settings.action.EXTRA_SETTINGS" />
83-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:69:17-85
83-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:69:25-82
84            </intent-filter>
85
86            <meta-data
86-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:72:13-74:77
87                android:name="com.android.settings.category"
87-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:73:17-61
88                android:value="com.android.settings.category.ia.homepage" />
88-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:74:17-74
89            <meta-data
89-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:75:13-77:70
90                android:name="com.android.settings.icon"
90-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:76:17-57
91                android:resource="@drawable/ic_settings_subscreen" />
91-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:77:17-67
92            <!-- 菜单标题 -->
93            <meta-data
93-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:79:13-81:52
94                android:name="com.android.settings.title"
94-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:80:17-58
95                android:value="@string/app_name" />
95-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:81:17-49
96        </activity>
97        <activity
97-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:84:9-89:55
98            android:name="com.yk.secondary.screen.settings.ui.ClockSelectActivity"
98-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:85:13-51
99            android:exported="false"
99-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:86:13-37
100            android:label="@string/subscreen_clock_select"
100-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:87:13-59
101            android:screenOrientation="portrait"
101-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:88:13-49
102            android:theme="@style/Theme.RBSettings" />
102-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:89:13-52
103        <activity
103-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:91:9-96:55
104            android:name="com.yk.secondary.screen.settings.ui.MusicActivity"
104-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:92:13-45
105            android:exported="false"
105-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:93:13-37
106            android:label="@string/subscreen_clock_select"
106-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:94:13-59
107            android:screenOrientation="portrait"
107-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:95:13-49
108            android:theme="@style/Theme.RBSettings" />
108-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:96:13-52
109        <activity
109-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:98:9-103:55
110            android:name="com.yk.secondary.screen.settings.ui.NofiAppsSelectActivity"
110-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:99:13-54
111            android:exported="false"
111-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:100:13-37
112            android:label="@string/subscreen_show_noti_apps_title"
112-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:101:13-67
113            android:screenOrientation="portrait"
113-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:102:13-49
114            android:theme="@style/Theme.RBSettings" />
114-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:103:13-52
115
116        <service
116-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:105:9-115:19
117            android:name="com.yk.secondary.screen.settings.service.NotificationObserverService"
117-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:106:13-64
118            android:excludeFromRecents="true"
118-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:107:13-46
119            android:exported="false"
119-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:108:13-37
120            android:label="@string/app_name"
120-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:109:13-45
121            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
121-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:110:13-87
122            android:stopWithTask="false" >
122-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:111:13-41
123            <intent-filter>
123-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:112:13-114:29
124                <action android:name="android.service.notification.NotificationListenerService" />
124-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:113:17-99
124-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:113:25-96
125            </intent-filter>
126        </service>
127        <service
127-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:116:9-124:19
128            android:name="com.yk.secondary.screen.settings.factorytest.FactoryTestService"
128-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:117:13-59
129            android:enabled="true"
129-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:118:13-35
130            android:exported="true" >
130-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:119:13-36
131            <intent-filter>
131-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:120:13-123:29
132                <action android:name="com.yk.aidl.factory.test" />
132-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:121:17-67
132-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:121:25-64
133
134                <category android:name="android.intent.category.DEFAULT" />
134-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:65:17-76
134-->G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:65:27-73
135            </intent-filter>
136        </service>
137        <service
137-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:24:9-33:19
138            android:name="androidx.camera.core.impl.MetadataHolderService"
138-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:25:13-75
139            android:enabled="false"
139-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:26:13-36
140            android:exported="false" >
140-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:27:13-37
141            <meta-data
141-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
142                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
142-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
143                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
143-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
144        </service>
145
146        <provider
146-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
147            android:name="androidx.startup.InitializationProvider"
147-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
148            android:authorities="com.yk.secondary.screen.settings.androidx-startup"
148-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
149            android:exported="false" >
149-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
150            <meta-data
150-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
151                android:name="androidx.emoji2.text.EmojiCompatInitializer"
151-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
152                android:value="androidx.startup" />
152-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
153            <meta-data
153-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
154                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
154-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
155                android:value="androidx.startup" />
155-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
156            <meta-data
156-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
157                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
157-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
158                android:value="androidx.startup" />
158-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
159        </provider>
160
161        <receiver
161-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
162            android:name="androidx.profileinstaller.ProfileInstallReceiver"
162-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
163            android:directBootAware="false"
163-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
164            android:enabled="true"
164-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
165            android:exported="true"
165-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
166            android:permission="android.permission.DUMP" >
166-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
168                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
168-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
168-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
171                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
171-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
171-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
172            </intent-filter>
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
174                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
174-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
174-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
175            </intent-filter>
176            <intent-filter>
176-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
177                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
177-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
177-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
178            </intent-filter>
179        </receiver>
180    </application>
181
182</manifest>
