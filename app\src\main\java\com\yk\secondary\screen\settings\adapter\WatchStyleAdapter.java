package com.yk.secondary.screen.settings.adapter;

import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_WATCH_SELECTION;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CLOCK_SELECT;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.ArrayList;

public class WatchStyleAdapter extends RecyclerView.Adapter<WatchStyleAdapter.MyViewHolder> {

    private final Context mContext;
    private final ArrayList<Drawable> mStyleList;
    private int index;//标记当前选择的选项

    public WatchStyleAdapter(Context context, ArrayList<Drawable> styleList) {
        this.mContext = context;
        this.mStyleList = styleList;
        index = SPUtils.getInstance().getInt(KEY_SUBSCREEN_CLOCK_SELECT, DEFAULT_WATCH_SELECTION);
    }

    @NonNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        @SuppressLint("InflateParams") View inflate = LayoutInflater.from(mContext).inflate(R.layout.item_watch_style, null);
        return new MyViewHolder(inflate);
    }

    @Override
    public void onBindViewHolder(MyViewHolder holder, @SuppressLint("RecyclerView") final int position) {
        holder.mIvStyle.setImageDrawable(mStyleList.get(position));
        holder.mRootView.setOnClickListener(new View.OnClickListener() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onClick(View v) {
                if (index == position) {
                    return;
                }
                index = position;
                SPUtils.getInstance().put(KEY_SUBSCREEN_CLOCK_SELECT, position);
                notifyDataSetChanged();
                if (!Utils.isOpenSecondaryScreen()) {
                    return;
                }

                SecondaryScreenBitmapManager.getInstance().switchWatchStyle();
            }
        });
        holder.mRbSelect.setChecked(index == position);
    }

    @Override
    public int getItemCount() {
        return mStyleList.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        private final LinearLayout mRootView;
        private final RadioButton mRbSelect;
        private final ImageView mIvStyle;

        public MyViewHolder(View itemView) {
            super(itemView);
            mRootView = itemView.findViewById(R.id.ll_root_view);
            mRbSelect = itemView.findViewById(R.id.rb_select);
            mIvStyle = itemView.findViewById(R.id.iv_style);
        }
    }

}