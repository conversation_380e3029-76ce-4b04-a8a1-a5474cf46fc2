<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@drawable/icon_watch_style4_bg"
    android:descendantFocusability="blocksDescendants">

    <ImageView
        android:id="@+id/iv_week"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="90px"
        android:layout_marginTop="46px" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="128px"
        android:layout_marginTop="48px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_date_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_date_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="68px"
        android:src="@drawable/icon_watch_style4_hour_min_tag" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="68px"
        android:layout_marginTop="68px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_time_h_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_time_h_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="127px"
        android:layout_marginTop="68px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_time_m_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_time_m_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <com.yk.secondary.screen.settings.view.WatchStyle4ClockView
        android:id="@+id/ac"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_width"
        android:layout_gravity="center"
        tools:ignore="MissingConstraints" />

</FrameLayout>