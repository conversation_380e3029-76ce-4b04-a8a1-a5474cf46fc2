package com.yk.secondary.screen.settings.manager;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_WATCH_SELECTION;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CLOCK_SELECT;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.util.Log;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.view.ClockHorizontalScrollView;
import com.yk.secondary.screen.settings.view.ClockListRelativeLayout;

import java.util.concurrent.atomic.AtomicBoolean;

public class ClockListViewManager {
    private ClockListViewManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final ClockListViewManager instance = new ClockListViewManager();
    }

    public static ClockListViewManager getInstance() {
        return ClockListViewManager.SingletonHolder.instance;
    }

    private ClockListRelativeLayout mRootView;

    private ClockHorizontalScrollView mClockHorizontalScrollView;

    private ImageView mIvPrev, mIvNext, mIvSet;

    public void setRootView(ClockListRelativeLayout rootView) {
        this.mRootView = rootView;
        initViews();
    }

    private final AtomicBoolean isShow = new AtomicBoolean(false);

    public boolean isShowClockListView() {
        return isShow.get();
    }

    public void initViews() {
        if (mRootView == null) {
            Log.i(TAG_LOG, "ClockListViewManager initViews:  mRootView is null ");
            return;
        }
        mClockHorizontalScrollView = mRootView.findViewById(R.id.clock_scroll_view);
        mRootView.setClockHorizontalScrollView(mClockHorizontalScrollView);
        mClockHorizontalScrollView.setChildGroup(mRootView.findViewById(R.id.container_clock));
        mClockHorizontalScrollView.setTvTag(mRootView.findViewById(R.id.clock_list_title));
        mIvPrev = mRootView.findViewById(R.id.clock_list_prev);
        mIvNext = mRootView.findViewById(R.id.clock_list_next);
        mIvSet = mRootView.findViewById(R.id.clock_list_set);

        mClockHorizontalScrollView.setOnScrollListener(page -> {
            if (page == 1) {
                mIvPrev.setVisibility(GONE);
            } else {
                mIvPrev.setVisibility(VISIBLE);
            }

            if (page == WatchViewManager.getInstance().WATCH_TOTAL_COUNT) {
                mIvNext.setVisibility(GONE);
            } else {
                mIvNext.setVisibility(VISIBLE);
            }

            if (page != SPUtils.getInstance().getInt(KEY_SUBSCREEN_CLOCK_SELECT, DEFAULT_WATCH_SELECTION) + 1) {
                mIvSet.setVisibility(GONE);
            } else {
                mIvSet.setVisibility(VISIBLE);
            }
        });
        mRootView.setOnClickListener(mIvPrev, id -> {
            if (mClockHorizontalScrollView != null) {
                mClockHorizontalScrollView.prevPage();
            }
        });

        mRootView.setOnClickListener(mIvNext, id -> {
            if (mClockHorizontalScrollView != null) {
                mClockHorizontalScrollView.nextPage();
            }
        });
    }

    public void showClockListView() {
        Log.i(TAG_LOG, "showClockListView: ");
        if (mRootView == null) {
            Log.i(TAG_LOG, "showClockListView: mRootView is null ");
            return;
        }
        mClockHorizontalScrollView.initImageView();
        int curPage = SPUtils.getInstance().getInt(KEY_SUBSCREEN_CLOCK_SELECT, DEFAULT_WATCH_SELECTION) + 1;
        if (curPage == 1) {
            mIvPrev.setVisibility(GONE);
        } else {
            mIvPrev.setVisibility(VISIBLE);
        }
        if (curPage == WatchViewManager.getInstance().WATCH_TOTAL_COUNT) {
            mIvNext.setVisibility(GONE);
        } else {
            mIvNext.setVisibility(VISIBLE);
        }
        mRootView.setVisibility(VISIBLE);
        isShow.set(true);
        Log.i(TAG_LOG, "showClockListView: removeSecondaryScreenSleepMsg ");
        SecondaryScreenControlManager.getInstance().removeSecondaryScreenSleepMsg();
    }

    public void hideClockListView() {
        Log.i(TAG_LOG, "hideClockListView: ");
        if (mRootView == null) {
            Log.i(TAG_LOG, "hideClockListView: mRootView is null ");
            return;
        }

        isShow.set(false);
        mClockHorizontalScrollView.onDestroy();
        mRootView.setVisibility(GONE);
        SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
    }

}
