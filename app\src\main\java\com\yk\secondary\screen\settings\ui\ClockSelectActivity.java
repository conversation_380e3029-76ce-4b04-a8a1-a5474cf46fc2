package com.yk.secondary.screen.settings.ui;

import android.annotation.SuppressLint;
import android.app.ActionBar;
import android.app.Activity;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.adapter.WatchStyleAdapter;

import java.util.ArrayList;

public class ClockSelectActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        showActionbarBackButton();
        setContentView(R.layout.activity_clock_select);
        initViews();
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private void initViews() {
        RecyclerView mRecyclerView = findViewById(R.id.recycler_view);
        ArrayList<Drawable> list = new ArrayList<>();
        list.add(getResources().getDrawable(R.drawable.icon_watch_style1));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style2));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style3));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style4));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style5));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style6));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style7));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style8));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style9));
        list.add(getResources().getDrawable(R.drawable.icon_watch_style10));
        WatchStyleAdapter watchStyleAdapter = new WatchStyleAdapter(this, list);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 2);
        mRecyclerView.setLayoutManager(gridLayoutManager);
        mRecyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                outRect.top = 0;
                outRect.left = 0;
                outRect.right = 0;
                outRect.bottom = 35;
            }
        });
        mRecyclerView.setAdapter(watchStyleAdapter);
    }

    private void showActionbarBackButton() {
        ActionBar actionBar = getActionBar();
        if (actionBar != null) {
            actionBar.setTitle(R.string.miniscreen_help_clock_select);
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeButtonEnabled(true);
            actionBar.setElevation(0);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            this.finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}