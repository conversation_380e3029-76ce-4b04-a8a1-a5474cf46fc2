package com.yk.secondary.screen.settings.view.watch;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.util.Log;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.Calendar;

public class WatchStyle1 extends BaseWatchStyle {

    private ImageView mIvTimeH1, mIvTimeH2, mIvTimeM1, mIvTimeM2, mIvWeek, mIvAm, mIvBattery, mIvDate1, mIvDate2;

    public WatchStyle1() {
        super();
    }

    @Override
    public void initViews() {
        mRootView = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_bitmap_watch_style1, null);

        mIvTimeH1 = mRootView.findViewById(R.id.iv_time_h_1);
        mIvTimeH2 = mRootView.findViewById(R.id.iv_time_h_2);
        setTimeH();

        mIvTimeM1 = mRootView.findViewById(R.id.iv_time_m_1);
        mIvTimeM2 = mRootView.findViewById(R.id.iv_time_m_2);
        setTimeM();

        mIvWeek = mRootView.findViewById(R.id.iv_week);
        setWeek();

        mIvAm = mRootView.findViewById(R.id.iv_am);
        setAm();

        mIvBattery = mRootView.findViewById(R.id.iv_battery);

        mIvDate1 = mRootView.findViewById(R.id.iv_date_1);
        mIvDate2 = mRootView.findViewById(R.id.iv_date_2);
        setDate();
    }

    @Override
    public int getStyleValue() {
        return 1;
    }

    @Override
    public void updateTimeChanged() {
        setTimeH();
        setTimeM();
        setWeek();
        setAm();
        setDate();
    }

    @Override
    public void updateDateChanged() {
        setTimeH();
        setTimeM();
        setWeek();
        setAm();
        setDate();
    }

    @Override
    public void updateCurrentLevelChanged(BatteryState batteryState) {
        setBattery(batteryState.getLevel());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void setTimeH() {
        String h = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH"));
        try {
            if (mIvTimeH1 != null) {
                mIvTimeH1.setImageResource(getHourImageResource(h.charAt(0)));
            }

            if (mIvTimeH2 != null) {
                mIvTimeH2.setImageResource(getHourImageResource(h.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeH: Exception " + e.getMessage());
        }
    }

    private void setTimeM() {
        String m = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("mm"));
        try {
            if (mIvTimeM1 != null) {
                mIvTimeM1.setImageResource(getMinImageResource(m.charAt(0)));
            }

            if (mIvTimeM2 != null) {
                mIvTimeM2.setImageResource(getMinImageResource(m.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeM: Exception " + e.getMessage());
        }
    }

    private void setWeek() {
        if (mIvWeek != null) {
            mIvWeek.setImageResource(getWeekImageResource());
        }
    }

    private void setAm() {
        if (mIvAm != null) {
            mIvAm.setImageResource(getAmOrPmImageResource());
        }
    }

    private void setBattery(int level) {
        if (mIvBattery != null) {
            mIvBattery.setImageResource(getBatteryImageResource(level));
        }
    }

    private void setDate() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("dd"));
        try {
            if (mIvDate1 != null) {
                mIvDate1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvDate2 != null) {
                mIvDate2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setDays: Exception " + e.getMessage());
        }
    }

    private int getHourImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style1_hour_0;
            case '1':
                return R.drawable.icon_watch_style1_hour_1;
            case '2':
                return R.drawable.icon_watch_style1_hour_2;
            case '3':
                return R.drawable.icon_watch_style1_hour_3;
            case '4':
                return R.drawable.icon_watch_style1_hour_4;
            case '5':
                return R.drawable.icon_watch_style1_hour_5;
            case '6':
                return R.drawable.icon_watch_style1_hour_6;
            case '7':
                return R.drawable.icon_watch_style1_hour_7;
            case '8':
                return R.drawable.icon_watch_style1_hour_8;
            case '9':
                return R.drawable.icon_watch_style1_hour_9;
            default:
                return R.drawable.icon_watch_style1_hour_0;
        }
    }

    private int getMinImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style1_min_0;
            case '1':
                return R.drawable.icon_watch_style1_min_1;
            case '2':
                return R.drawable.icon_watch_style1_min_2;
            case '3':
                return R.drawable.icon_watch_style1_min_3;
            case '4':
                return R.drawable.icon_watch_style1_min_4;
            case '5':
                return R.drawable.icon_watch_style1_min_5;
            case '6':
                return R.drawable.icon_watch_style1_min_6;
            case '7':
                return R.drawable.icon_watch_style1_min_7;
            case '8':
                return R.drawable.icon_watch_style1_min_8;
            case '9':
                return R.drawable.icon_watch_style1_min_9;
            default:
                return R.drawable.icon_watch_style1_min_0;
        }
    }

    private int getDateImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style1_date_0;
            case '1':
                return R.drawable.icon_watch_style1_date_1;
            case '2':
                return R.drawable.icon_watch_style1_date_2;
            case '3':
                return R.drawable.icon_watch_style1_date_3;
            case '4':
                return R.drawable.icon_watch_style1_date_4;
            case '5':
                return R.drawable.icon_watch_style1_date_5;
            case '6':
                return R.drawable.icon_watch_style1_date_6;
            case '7':
                return R.drawable.icon_watch_style1_date_7;
            case '8':
                return R.drawable.icon_watch_style1_date_8;
            case '9':
                return R.drawable.icon_watch_style1_date_9;
            default:
                return R.drawable.icon_watch_style1_date_0;
        }
    }

    private int getBatteryImageResource(int level) {
        if (level <= 0) {
            return R.drawable.icon_watch_style1_battery_0;
        } else if (level <= 10) {
            return R.drawable.icon_watch_style1_battery_10;
        } else if (level <= 20) {
            return R.drawable.icon_watch_style1_battery_20;
        } else if (level <= 30) {
            return R.drawable.icon_watch_style1_battery_30;
        } else if (level <= 40) {
            return R.drawable.icon_watch_style1_battery_40;
        } else if (level <= 50) {
            return R.drawable.icon_watch_style1_battery_50;
        } else if (level <= 60) {
            return R.drawable.icon_watch_style1_battery_60;
        } else if (level <= 70) {
            return R.drawable.icon_watch_style1_battery_70;
        } else if (level <= 80) {
            return R.drawable.icon_watch_style1_battery_80;
        } else if (level <= 90) {
            return R.drawable.icon_watch_style1_battery_90;
        } else if (level >= 100) {
            return R.drawable.icon_watch_style1_battery_100;
        }
        return R.drawable.icon_watch_style1_battery_100;
    }

    private int getAmOrPmImageResource() {
        boolean isAm = Utils.isAm();
        boolean isChinese = Utils.isChinese(App.getInstance().getContext());
        if (isAm) {
            if (isChinese) {
                return R.drawable.icon_watch_style1_am_zh;
            } else {
                return R.drawable.icon_watch_style1_am_en;
            }
        } else {
            if (isChinese) {
                return R.drawable.icon_watch_style1_pm_zh;
            } else {
                return R.drawable.icon_watch_style1_pm_en;
            }
        }
    }

    private int getWeekImageResource() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        boolean isChinese = Utils.isChinese(App.getInstance().getContext());
        switch (dayOfWeek) {
            case 1:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_sun_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_sun_en;
                }
            case 2:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_mon_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_mon_en;
                }
            case 3:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_tue_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_tue_en;
                }
            case 4:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_wed_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_wed_en;
                }
            case 5:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_thu_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_thu_en;
                }
            case 6:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_fri_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_fri_en;
                }
            case 7:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_sat_en;
                }
            default:
                if (isChinese) {
                    return R.drawable.icon_watch_style1_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style1_week_sat_en;
                }
        }
    }
}
