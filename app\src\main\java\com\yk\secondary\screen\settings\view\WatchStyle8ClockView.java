package com.yk.secondary.screen.settings.view;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;

import com.yk.secondary.screen.settings.R;

import java.util.Calendar;

public class WatchStyle8ClockView extends View {

    private final Paint mPaint;

    //控件宽度
    public int mWidth;

    //控件高度
    public int mHeight;

    public WatchStyle8ClockView(Context context) {
        this(context, null);
    }

    public WatchStyle8ClockView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WatchStyle8ClockView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.STROKE);
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(measureSize(widthMeasureSpec), measureSize(heightMeasureSpec));
    }

    private int measureSize(int mMeasureSpec) {
        int result;
        int mode = MeasureSpec.getMode(mMeasureSpec);
        int size = MeasureSpec.getSize(mMeasureSpec);
        if (mode == MeasureSpec.EXACTLY) {
            result = size;
        } else {
            result = 400;
            if (mode == MeasureSpec.AT_MOST) {
                result = Math.min(result, size);
            }
        }
        return result;
    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);
        //设置宽高、半径
        mWidth = getMeasuredWidth() - getPaddingLeft() - getPaddingRight();
        mHeight = getMeasuredHeight() - getPaddingBottom() - getPaddingTop();
        //绘制指针
        drawPointer(canvas);
    }

    /**
     * 绘制指针
     */
    private void drawPointer(Canvas canvas) {
        Calendar mCalendar = Calendar.getInstance();
        //获取当前小时数
        int hours = mCalendar.get(Calendar.HOUR);
        //获取当前分钟数
        int minutes = mCalendar.get(Calendar.MINUTE);
        //获取当前秒数
        int seconds = mCalendar.get(Calendar.SECOND);

        //绘制背景
//        canvas.save();
//        canvas.drawBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.icon_watch_style1_bg), 0, 0, mPaint);
//        canvas.restore();

        //绘制时针
        canvas.save();
        //这里计算时针需要旋转的角度 实现原理是计算出一共多少分钟除以60计算出真实的小时数（带有小数，为了更加准确计算度数），已知12小时是360度，现在求出了实际小时数比例求出角度
        float hoursAngle = (hours * 60 + minutes) / 60f / 12f * 360;
        canvas.rotate(hoursAngle, (float) mWidth / 2, (float) mHeight / 2);
        canvas.drawBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.icon_watch_style8_time_h), 0, 0, mPaint);
        canvas.restore();

        //绘制分针
        canvas.save();
        //这里计算分针需要旋转的角度  60分钟360度，求出实际分钟数所占的度数
        float minutesAngle = (minutes * 60 + seconds) / 60f / 60f * 360;
        canvas.rotate(minutesAngle, (float) mWidth / 2, (float) mHeight / 2);
        canvas.drawBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.icon_watch_style8_time_m), 0, 0, mPaint);
        canvas.restore();

        //绘制秒针
        canvas.save();
        mPaint.setStyle(Paint.Style.STROKE);
        //这里计算秒针需要旋转的角度  60秒360度，求出实际秒数所占的度数
        float secondAngle = seconds / 60f * 360;
        canvas.rotate(secondAngle, (float) mWidth / 2, (float) mHeight / 2);
        canvas.drawBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.icon_watch_style8_time_s), 0, 0, mPaint);
        canvas.restore();
    }
}

