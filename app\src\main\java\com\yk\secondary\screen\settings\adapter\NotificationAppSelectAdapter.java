package com.yk.secondary.screen.settings.adapter;


import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.AppUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class NotificationAppSelectAdapter extends BaseAdapter {
    private final ArrayList<String> mAppInfoList = new ArrayList<>();

    public void addAppInfoList(List<String> list) {
        mAppInfoList.clear();
        mAppInfoList.addAll(list);
    }

    @Override
    public int getCount() {
        return mAppInfoList.size();
    }

    @Override
    public String getItem(int i) {
        return this.mAppInfoList.get(i);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        View view;
        ViewHolder viewHolder;
        if (convertView == null) {
            viewHolder = new ViewHolder();
            view = LayoutInflater.from(App.getInstance().getContext()).inflate(R.layout.item_app_notifi_select_view, (ViewGroup) null);
            viewHolder.rl_item_view = view.findViewById(R.id.rl_item_view);
            viewHolder.icon_app_select = view.findViewById(R.id.icon_app_select);
            viewHolder.tv_item_app_label = view.findViewById(R.id.tv_item_app_label);
            viewHolder.item_RadioButton = view.findViewById(R.id.smart_key_state);
            view.setTag(viewHolder);
        } else {
            view = convertView;
            viewHolder = (ViewHolder) convertView.getTag();
        }

        String packageName = mAppInfoList.get(position);
        if (!TextUtils.isEmpty(packageName)) {
            Drawable drawable = AppUtils.getAppIcon(packageName);
            if (drawable != null) {
                viewHolder.icon_app_select.setImageDrawable(drawable);
            }
            viewHolder.tv_item_app_label.setText(AppUtils.getAppName(packageName));

            viewHolder.rl_item_view.setOnClickListener(v -> {
                if (!Utils.getSubScreenSelectedNotiApps().contains(packageName)) {
                    String selectNofiAppsStr = Utils.getSubScreenSelectedNotiApps();
                    String[] selectNofiApps = selectNofiAppsStr.split(":");
                    ArrayList<String> list = new ArrayList<>();
                    for (String str : selectNofiApps) {
                        list.add(str);
                    }
                    list.add(packageName);
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int i = 0; i < list.size(); i++) {
                        stringBuilder.append(list.get(i));
                        if (i != list.size() - 1) {
                            stringBuilder.append(":");
                        }
                    }
                    Utils.setSubScreenSelectedNotiApps(stringBuilder.toString());
                } else {
                    String selectNofiAppsStr = Utils.getSubScreenSelectedNotiApps();
                    String[] selectNofiApps = selectNofiAppsStr.split(":");
                    ArrayList<String> list = new ArrayList<>();
                    Collections.addAll(list, selectNofiApps);
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int i = 0; i < list.size(); i++) {
                        if (TextUtils.equals(list.get(i), packageName)) {
                            continue;
                        }
                        stringBuilder.append(list.get(i));
                        if (i != list.size() - 1) {
                            stringBuilder.append(":");
                        }
                    }
                    Utils.setSubScreenSelectedNotiApps(stringBuilder.toString());
                }

                notifyDataSetChanged();
            });

            viewHolder.item_RadioButton.setOnCheckedChangeListener(null);
            viewHolder.item_RadioButton.setChecked(Utils.getSubScreenSelectedNotiApps().contains(packageName));
        }
        return view;
    }

    private static class ViewHolder {
        RelativeLayout rl_item_view;
        ImageView icon_app_select;
        TextView tv_item_app_label;
        CheckBox item_RadioButton;
    }
}

