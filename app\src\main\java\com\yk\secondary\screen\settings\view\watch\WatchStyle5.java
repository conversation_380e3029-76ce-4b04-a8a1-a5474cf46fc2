package com.yk.secondary.screen.settings.view.watch;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Shader;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.Calendar;

public class WatchStyle5 extends BaseWatchStyle {

    private ImageView mIvWeek, mIvMonth1, mIvMonth2,mIvDay1, mIvDay2, mIvTimeH1, mIvTimeH2, mIvTimeM1, mIvTimeM2, mIvBattery;

    public WatchStyle5() {
        super();
    }

    @Override
    public void initViews() {
        mRootView = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_bitmap_watch_style5, null);
        mIvTimeH1 = mRootView.findViewById(R.id.iv_time_h_1);
        mIvTimeH2 = mRootView.findViewById(R.id.iv_time_h_2);
        setTimeH();

        mIvTimeM1 = mRootView.findViewById(R.id.iv_time_m_1);
        mIvTimeM2 = mRootView.findViewById(R.id.iv_time_m_2);
        setTimeM();

        mIvDay1 = mRootView.findViewById(R.id.iv_day_1);
        mIvDay2 = mRootView.findViewById(R.id.iv_day_2);
        setDay();

        mIvMonth1 = mRootView.findViewById(R.id.iv_month_1);
        mIvMonth2 = mRootView.findViewById(R.id.iv_month_2);
        setMonth();

        mIvWeek = mRootView.findViewById(R.id.iv_week);
        setWeek();

        mIvBattery = mRootView.findViewById(R.id.iv_battery);
    }

    @Override
    public int getStyleValue() {
        return 5;
    }

    @Override
    public void updateTimeChanged() {
        setTimeH();
        setTimeM();
        setMonth();
        setDay();
        setWeek();
    }

    @Override
    public void updateDateChanged() {
        setTimeH();
        setTimeM();
        setMonth();
        setDay();
        setWeek();
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void updateCurrentLevelChanged(BatteryState batteryState) {
        setBattery(batteryState.getLevel());
    }

    private void setWeek() {
        if (mIvWeek != null) {
            mIvWeek.setImageResource(getWeekImageResource());
        }
    }

    private void setDay() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("dd"));
        try {
            if (mIvDay1 != null) {
                mIvDay1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvDay2 != null) {
                mIvDay2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setDays: Exception " + e.getMessage());
        }
    }

    private void setMonth() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("MM"));
        try {
            if (mIvMonth1 != null) {
                mIvMonth1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvMonth2 != null) {
                mIvMonth2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setMonth: Exception " + e.getMessage());
        }
    }
    
    private void setTimeH() {
        String h = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH"));
        try {
            if (mIvTimeH1 != null) {
                mIvTimeH1.setImageResource(getHourImageResource(h.charAt(0)));
            }

            if (mIvTimeH2 != null) {
                mIvTimeH2.setImageResource(getHourImageResource(h.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeH: Exception " + e.getMessage());
        }
    }

    private void setTimeM() {
        String m = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("mm"));
        try {
            if (mIvTimeM1 != null) {
                mIvTimeM1.setImageResource(getMin1ImageResource(m.charAt(0)));
            }

            if (mIvTimeM2 != null) {
                mIvTimeM2.setImageResource(getMin2ImageResource(m.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeM: Exception " + e.getMessage());
        }
    }

    private void setBattery(int level) {
        if (mIvBattery != null) {
            mIvBattery.setImageResource(getBatteryImageResource(level));
        }
    }

    private int getHourImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style5_hour_0;
            case '1':
                return R.drawable.icon_watch_style5_hour_1;
            case '2':
                return R.drawable.icon_watch_style5_hour_2;
            case '3':
                return R.drawable.icon_watch_style5_hour_3;
            case '4':
                return R.drawable.icon_watch_style5_hour_4;
            case '5':
                return R.drawable.icon_watch_style5_hour_5;
            case '6':
                return R.drawable.icon_watch_style5_hour_6;
            case '7':
                return R.drawable.icon_watch_style5_hour_7;
            case '8':
                return R.drawable.icon_watch_style5_hour_8;
            case '9':
                return R.drawable.icon_watch_style5_hour_9;
            default:
                return R.drawable.icon_watch_style5_hour_0;
        }
    }

    private int getMin1ImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style5_min1_0;
            case '1':
                return R.drawable.icon_watch_style5_min1_1;
            case '2':
                return R.drawable.icon_watch_style5_min1_2;
            case '3':
                return R.drawable.icon_watch_style5_min1_3;
            case '4':
                return R.drawable.icon_watch_style5_min1_4;
            case '5':
                return R.drawable.icon_watch_style5_min1_5;
            case '6':
                return R.drawable.icon_watch_style5_min1_6;
            case '7':
                return R.drawable.icon_watch_style5_min1_7;
            case '8':
                return R.drawable.icon_watch_style5_min1_8;
            case '9':
                return R.drawable.icon_watch_style5_min1_9;
            default:
                return R.drawable.icon_watch_style5_min1_0;
        }
    }

    private int getMin2ImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style5_min2_0;
            case '1':
                return R.drawable.icon_watch_style5_min2_1;
            case '2':
                return R.drawable.icon_watch_style5_min2_2;
            case '3':
                return R.drawable.icon_watch_style5_min2_3;
            case '4':
                return R.drawable.icon_watch_style5_min2_4;
            case '5':
                return R.drawable.icon_watch_style5_min2_5;
            case '6':
                return R.drawable.icon_watch_style5_min2_6;
            case '7':
                return R.drawable.icon_watch_style5_min2_7;
            case '8':
                return R.drawable.icon_watch_style5_min2_8;
            case '9':
                return R.drawable.icon_watch_style5_min2_9;
            default:
                return R.drawable.icon_watch_style5_min2_0;
        }
    }

    private int getDateImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style5_date_0;
            case '1':
                return R.drawable.icon_watch_style5_date_1;
            case '2':
                return R.drawable.icon_watch_style5_date_2;
            case '3':
                return R.drawable.icon_watch_style5_date_3;
            case '4':
                return R.drawable.icon_watch_style5_date_4;
            case '5':
                return R.drawable.icon_watch_style5_date_5;
            case '6':
                return R.drawable.icon_watch_style5_date_6;
            case '7':
                return R.drawable.icon_watch_style5_date_7;
            case '8':
                return R.drawable.icon_watch_style5_date_8;
            case '9':
                return R.drawable.icon_watch_style5_date_9;
            default:
                return R.drawable.icon_watch_style5_date_0;
        }
    }

    private int getWeekImageResource() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        boolean isChinese = Utils.isChinese(App.getInstance().getContext());
        switch (dayOfWeek) {
            case 1:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_sun_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_sun_en;
                }
            case 2:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_mon_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_mon_en;
                }
            case 3:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_tue_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_tue_en;
                }
            case 4:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_wed_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_wed_en;
                }
            case 5:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_thu_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_thu_en;
                }
            case 6:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_fri_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_fri_en;
                }
            case 7:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_sat_en;
                }
            default:
                if (isChinese) {
                    return R.drawable.icon_watch_style5_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style5_week_sat_en;
                }
        }
    }

    private int getBatteryImageResource(int level) {
        if (level <= 0) {
            return R.drawable.icon_watch_style5_battery_0;
        } else if (level <= 10) {
            return R.drawable.icon_watch_style5_battery_10;
        } else if (level <= 20) {
            return R.drawable.icon_watch_style5_battery_20;
        } else if (level <= 30) {
            return R.drawable.icon_watch_style5_battery_30;
        } else if (level <= 40) {
            return R.drawable.icon_watch_style5_battery_40;
        } else if (level <= 50) {
            return R.drawable.icon_watch_style5_battery_50;
        } else if (level <= 60) {
            return R.drawable.icon_watch_style5_battery_60;
        } else if (level <= 70) {
            return R.drawable.icon_watch_style5_battery_70;
        } else if (level <= 80) {
            return R.drawable.icon_watch_style5_battery_80;
        } else if (level <= 90) {
            return R.drawable.icon_watch_style5_battery_90;
        } else if (level >= 100) {
            return R.drawable.icon_watch_style5_battery_100;
        }
        return R.drawable.icon_watch_style5_battery_100;
    }
}
