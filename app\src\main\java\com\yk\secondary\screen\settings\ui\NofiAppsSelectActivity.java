package com.yk.secondary.screen.settings.ui;

import android.annotation.SuppressLint;
import android.app.ActionBar;
import android.app.Activity;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.ListView;

import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.adapter.NotificationAppSelectAdapter;
import com.yk.secondary.screen.settings.utils.AppUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.ArrayList;

public class NofiAppsSelectActivity extends Activity {

    private NotificationAppSelectAdapter mNotificationAppSelectAdapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        showActionbarBackButton();
        setContentView(R.layout.activity_musci_app);
        initViews();
        initData();
    }

    private void initViews() {
        ListView listView = findViewById(R.id.app_list);
        mNotificationAppSelectAdapter = new NotificationAppSelectAdapter();
        listView.setAdapter(mNotificationAppSelectAdapter);
    }

    private void initData() {
        ThreadUtils.executeByCpu(new ThreadUtils.SimpleTask<ArrayList<String>>() {
            @SuppressLint("QueryPermissionsNeeded")
            @Override
            public ArrayList<String> doInBackground() {
                String selectNofiAppsStr = Utils.getSubScreenSelectedNotiApps();
                String[] selectNofiApps = selectNofiAppsStr.split(":");
                ArrayList<String> list = new ArrayList<>();
                for (String str : selectNofiApps) {
                    if (AppUtils.isAppInstalled(str)) {
                        list.add(str);
                    }
                }
                for (AppUtils.AppInfo appInfo : AppUtils.getAppsInfo()) {
                    String packageName = appInfo.getPackageName();
                    if (!list.contains(packageName)) {
                        list.add(packageName);
                    }
                }
                return list;
            }

            @Override
            public void onSuccess(ArrayList<String> result) {
                if (mNotificationAppSelectAdapter != null) {
                    mNotificationAppSelectAdapter.addAppInfoList(result);
                    mNotificationAppSelectAdapter.notifyDataSetChanged();
                }
            }
        }, Thread.MAX_PRIORITY);
    }

    private void showActionbarBackButton() {
        ActionBar actionBar = getActionBar();
        if (actionBar != null) {
            actionBar.setTitle(R.string.subscreen_show_noti_apps_title);
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeButtonEnabled(true);
            actionBar.setElevation(0);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            this.finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
