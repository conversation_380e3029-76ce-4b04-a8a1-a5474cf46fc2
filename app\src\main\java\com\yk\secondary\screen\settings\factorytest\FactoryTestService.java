package com.yk.secondary.screen.settings.factorytest;

import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL7;
import static com.yk.secondary.screen.settings.utils.Constant.PATH_SECONDARY_BRIGHTNESS_NODES;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.IFactoryTestAidlInterface;
import com.yk.secondary.screen.settings.IRegisterResultCallback;
import com.yk.secondary.screen.settings.manager.FactoryTestManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;
import com.yk.secondary.screen.settings.utils.FileIOUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;

public class FactoryTestService extends Service {

    private final IFactoryTestAidlInterface.Stub mBinder = new IFactoryTestAidlInterface.Stub() {
        @Override
        public void startFactory() {
            ThreadUtils.runOnUiThread(() -> FactoryTestManager.getInstance().startFactoryTest());
        }

        @Override
        public void registerResultCallback(IRegisterResultCallback callback) {
            Log.i(TAG_LOG, "FactoryTestService registerResultCallback: ");
            FactoryTestManager.getInstance().setIRegisterResultCallback(callback);
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return mBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        //停止测试
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                FileIOUtils.writeFileFromString(PATH_SECONDARY_BRIGHTNESS_NODES, String.valueOf(BRIGHTNESS_LEVEL7));
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
        FactoryTestManager.getInstance().stopFactoryTest();
        FactoryTestManager.getInstance().setIRegisterResultCallback(null);
        SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
        return super.onUnbind(intent);
    }
}
