package com.yk.framework.lib;

import android.os.IBinder;
import android.os.RemoteException;
import android.os.ServiceManager;
import android.app.Notification;
import android.app.INotificationManager;
import android.app.NotificationChannelGroup;
import android.content.pm.ParceledListSlice;
import android.util.Log;
import java.util.ArrayList;
import java.util.List;

public class NotificationManagerUtils {
    private static INotificationManager sService;

    static public INotificationManager getService()
    {
        if (sService != null) {
            return sService;
        }
        IBinder b = ServiceManager.getService("notification");
        sService = INotificationManager.Stub.asInterface(b);
        return sService;
    }

    public boolean isNotificationPolicyAccessGranted(String packageName) {
        INotificationManager service = getService();
        try {
            return service.isNotificationPolicyAccessGranted(packageName);
        } catch (RemoteException e) {
            Log.i("NotificationManagerUtils", "isNotificationPolicyAccessGranted: " + e.getMessage());
        }
        return false;
    }

    public boolean areNotificationsEnabled(String packageName) {
        INotificationManager service = getService();
        try {
            return service.areNotificationsEnabled(packageName);
        } catch (RemoteException e) {
            Log.i("NotificationManagerUtils", "areNotificationsEnabled: " + e.getMessage());
        }
        return false;
    }

    public List<NotificationChannelGroup> getNotificationChannelGroups(String packageName) {
        INotificationManager service = getService();
        try {
            final ParceledListSlice<NotificationChannelGroup> parceledList =
                    service.getNotificationChannelGroups(packageName);
            if (parceledList != null) {
                return parceledList.getList();
            }
        } catch (RemoteException e) {
            Log.i("NotificationManagerUtils", "getNotificationChannelGroups: " + e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<NotificationChannelGroup> getGroups(String pkg, int uid) {
        INotificationManager service = getService();
        try {
            return service.getNotificationChannelGroupsForPackage(pkg, uid, false).getList();
        } catch (Exception e) {
            return new ArrayList<NotificationChannelGroup>();
        }
    }

    public boolean getNotificationsBanned(String pkg, int uid) {
        INotificationManager service = getService();
        try {
            final boolean enabled = service.areNotificationsEnabledForPackage(pkg, uid);
            return !enabled;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isMediaNotification(Notification notification) {
        if (notification == null) {
            return false;
        }
        return notification.isMediaNotification();
    }
}
