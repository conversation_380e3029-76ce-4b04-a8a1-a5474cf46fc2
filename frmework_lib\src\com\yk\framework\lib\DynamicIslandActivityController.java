package com.yk.framework.lib;


import android.app.IActivityController;
import android.content.Intent;
import android.os.RemoteException;

public class DynamicIslandActivityController extends IActivityController.Stub {

    private DynamicIslandActivityControllerListener mDynamicIslandActivityControllerListener;

    public DynamicIslandActivityController(DynamicIslandActivityControllerListener dynamicIslandActivityControllerListener) {
        this.mDynamicIslandActivityControllerListener = dynamicIslandActivityControllerListener;
    }

    public void unregisterActivityController() {
        this.mDynamicIslandActivityControllerListener = null;
    }

    public boolean activityResuming(String pkgName) throws RemoteException {
        if (this.mDynamicIslandActivityControllerListener != null) {
            this.mDynamicIslandActivityControllerListener.activityResuming(pkgName);
        }

        return true;
    }

    public boolean activityStarting(Intent intent, String pkgName) throws RemoteException {
        if (this.mDynamicIslandActivityControllerListener != null) {
            this.mDynamicIslandActivityControllerListener.activityStarting(pkgName);
        }

        return true;
    }

    public boolean appCrashed(String arg0, int arg1, String arg2, String arg3, long arg4, String arg5) throws RemoteException {
        return false;
    }

    public int appEarlyNotResponding(String arg0, int arg1, String arg2) throws RemoteException {
        return 0;
    }

    public int appNotResponding(String arg0, int arg1, String arg2) throws RemoteException {
        return 0;
    }

    public int systemNotResponding(String arg0) throws RemoteException {
        return 0;
    }

}