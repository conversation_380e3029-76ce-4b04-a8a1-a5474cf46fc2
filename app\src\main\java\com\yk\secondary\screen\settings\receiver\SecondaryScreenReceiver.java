package com.yk.secondary.screen.settings.receiver;


import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE_IN;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_OPEN;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_OPEN_IN;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BRIGHT_SCREEN_MODE_DOUBLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BRIGHT_SCREEN_MODE_FLIP;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BRIGHT_SCREEN_MODE_HOME;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_DOUBLE_TOUCH_KEY;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_FLIP_SCREENON;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_WITH_MAINSCREEN;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.yk.secondary.screen.settings.manager.NotificationViewManager;
import com.yk.secondary.screen.settings.manager.RemindersViewManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.utils.Utils;


public class SecondaryScreenReceiver extends BroadcastReceiver {

    public SecondaryScreenReceiver() {
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }

        String action = intent.getAction();
        Log.i(TAG_LOG, "SecondaryScreenReceiver onReceive:action  " + action);
        if (TextUtils.equals(Intent.ACTION_LOCALE_CHANGED,action)) {
            NotificationViewManager.getInstance().updateLanguage();
        } else if (TextUtils.equals(Intent.ACTION_SCREEN_ON, action)) {
            if (!Utils.isOpenSecondaryScreen()) {
                return;
            }

            if (!SPUtils.getInstance().getBoolean(KEY_SUBSCREEN_WITH_MAINSCREEN, DEFAULT_BRIGHT_SCREEN_MODE_HOME)) {
                return;
            }

            int screenStatus = SecondaryScreenControlManager.getInstance().getScreenStatus();
            if (screenStatus == STATUS_SCREEN_CLOSE || screenStatus == STATUS_SCREEN_CLOSE_IN) {
                SecondaryScreenControlManager.getInstance().switchSecondaryScreen(true, null);
            }
        }
        if (TextUtils.equals("android.intent.action.PHONE_STATE", action)) {
            if (!Utils.isOpenSecondaryScreen()) {
                return;
            }

            // 从Intent中获取电话状态
            String state = intent.getStringExtra(TelephonyManager.EXTRA_STATE);
            if (!TextUtils.isEmpty(state)) {
                RemindersViewManager.getInstance().setPhotoState(state);
            }
        } else if (Intent.ACTION_SHUTDOWN.equals(action)) {
            if (!Utils.isOpenSecondaryScreen()) {
                return;
            }

            //关机，关闭副屏
            int screenStatus = SecondaryScreenControlManager.getInstance().getScreenStatus();
            if (screenStatus == STATUS_SCREEN_OPEN || screenStatus == STATUS_SCREEN_OPEN_IN) {
                SecondaryScreenControlManager.getInstance().switchSecondaryScreen(false, null);
            }
        } else if ("yk.secondary.screen.smart.warkup".equals(action)) {
            if (!Utils.isOpenSecondaryScreen()) {
                return;
            }

            int screenStatus = SecondaryScreenControlManager.getInstance().getScreenStatus();
            if (screenStatus == STATUS_SCREEN_CLOSE || screenStatus == STATUS_SCREEN_CLOSE_IN) {
                SecondaryScreenControlManager.getInstance().switchSecondaryScreen(true, null);
            }
        } else if ("yk.secondary.screen.double.warkup".equals(action)) {
            if (!Utils.isOpenSecondaryScreen()) {
                Log.i(TAG_LOG, "onReceive: 副屏总开关关闭 ");
                return;
            }

            if (!SPUtils.getInstance().getBoolean(KEY_DOUBLE_TOUCH_KEY, DEFAULT_BRIGHT_SCREEN_MODE_DOUBLE)) {
                Log.i(TAG_LOG, "onReceive: 双击唤醒开关关闭 ");
                return;
            }

            int screenStatus = SecondaryScreenControlManager.getInstance().getScreenStatus();
            Log.i(TAG_LOG, "onReceive: double.warkup screenStatus " + screenStatus);
            if (screenStatus == STATUS_SCREEN_CLOSE || screenStatus == STATUS_SCREEN_CLOSE_IN) {
                SecondaryScreenControlManager.getInstance().switchSecondaryScreen(true, null);
            }
        }
    }
}
