package com.yk.secondary.screen.settings;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.util.Log;
import android.view.MotionEvent;

import com.yk.secondary.screen.settings.listener.EventCallback;
import com.yk.secondary.screen.settings.utils.ThreadUtils;

import java.util.concurrent.atomic.AtomicBoolean;

public class App extends Application {

    @SuppressLint("StaticFieldLeak")
    private Context mContext;
    @SuppressLint("StaticFieldLeak")
    private static App mInstance;

    private String mCurPackageName = "";

    public void setCurPackageName(String mCurPackageName) {
        this.mCurPackageName = mCurPackageName;
    }

    public String getCurPackageName() {
        return mCurPackageName;
    }

    private EventCallback mEventCallback;

    private final AtomicBoolean mIsEventListener = new AtomicBoolean(false);

    static {
        System.loadLibrary("lib_secondary_screen");
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mInstance = this;
        mContext = getApplicationContext();
        Log.i("inCar_Screen", "Application onCreate:");
    }

    public Context getContext() {
        return mContext;
    }

    public static App getInstance() {
        return mInstance;
    }

    private boolean firstDownAction = false;

    /**
     * JNI回调方法
     */
    private void onKeyEvent(int action, int x, int y, long downTime, long eventTime) {
        if (firstDownAction && action == MotionEvent.ACTION_DOWN) {
            action = MotionEvent.ACTION_MOVE;
        }

        if (action == MotionEvent.ACTION_DOWN) {
            firstDownAction = true;
        }

        if (action == MotionEvent.ACTION_UP) {
            firstDownAction = false;
        }

        if (mEventCallback != null) {
            mEventCallback.onKeyEvent(action, x, y, downTime, eventTime);
        }
    }

    /**
     * 副屏亮屏开始监听
     */
    public synchronized void startSecondaryScreenEventListener(EventCallback callback) {
        mIsEventListener.set(true);
        this.mEventCallback = callback;
        ThreadUtils.executeByCpu(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                startSecondaryScreenEvent();
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
    }

    /**
     * 灭屏停止监听
     */
    public synchronized void stopSecondaryScreenEventListener() {
        if (!mIsEventListener.get()) {
            return;
        }
        mIsEventListener.set(false);
        this.mEventCallback = null;
        ThreadUtils.executeByCpu(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                stopSecondaryScreenEvent();
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
    }

    private native void startSecondaryScreenEvent();

    private native void stopSecondaryScreenEvent();
}
