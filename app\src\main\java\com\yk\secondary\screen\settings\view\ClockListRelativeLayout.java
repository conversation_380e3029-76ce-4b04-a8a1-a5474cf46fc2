package com.yk.secondary.screen.settings.view;

import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CLOCK_SELECT;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.ClockListViewManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;

import java.util.HashMap;

public class ClockListRelativeLayout extends RelativeLayout {

    private final HashMap<Integer, OnClickListener> mClickViews = new HashMap<>();

    public void setOnClickListener(@NonNull View view, OnClickListener listener) {
        mClickViews.put(view.getId(), listener);
    }

    private ClockHorizontalScrollView mClockHorizontalScrollView;

    public void setClockHorizontalScrollView(ClockHorizontalScrollView clockHorizontalScrollView) {
        this.mClockHorizontalScrollView = clockHorizontalScrollView;
    }

    private GestureDetector mGestureDetector;

    public ClockListRelativeLayout(Context mContext, AttributeSet attrs, int defStyle) {
        super(mContext, attrs, defStyle);
        initDoubleTapListener(mContext);
    }

    public ClockListRelativeLayout(Context mContext, AttributeSet attrs) {
        super(mContext, attrs);
        initDoubleTapListener(mContext);
    }

    public ClockListRelativeLayout(Context mContext) {
        super(mContext);
        initDoubleTapListener(mContext);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (mGestureDetector != null) {
            try {
                mGestureDetector.onTouchEvent(ev);
            } catch (Exception e) {
                Log.e(TAG_LOG, "Error in gesture detector: " + e.getMessage());
            }
        }
        if (mClockHorizontalScrollView != null && ClockListViewManager.getInstance().isShowClockListView()) {
            try {
                mClockHorizontalScrollView.onTouchEvent(ev);
            } catch (Exception e) {
                Log.e(TAG_LOG, "Error in scroll view: " + e.getMessage());
            }
        }
        return true;
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {

            @Override
            public boolean onSingleTapUp(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "ClockListRelativeLayout onSingleTapUp: ");
                try {
                    float x = e.getX();
                    float y = e.getY();
                    if (mClickViews.isEmpty()) {
                        return super.onSingleTapUp(e);
                    }
                    mClickViews.forEach((id, listener) -> {
                        if (id == R.id.clock_list_prev) {//上一个表盘
                            if (x >= 0 && x <= 50 && y >= 110 && y <= 175) {
                                listener.onClick(id);
                            }
                        } else if (id == R.id.clock_list_next) {//下一个表盘
                            if (x >= 190 && x <= 240 && y >= 110 && y <= 175) {
                                listener.onClick(id);
                            }
                        }
                    });
                } catch (Exception exception) {
                    Log.i(TAG_LOG, "ClockListRelativeLayout onSingleTapUp: exception " + exception.getMessage());
                }
                return super.onSingleTapUp(e);
            }

            @Override
            public boolean onDoubleTap(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "ClockListRelativeLayout onDoubleTap: ");
                ThreadUtils.runOnUiThread(() -> {
                    if (ClockListViewManager.getInstance().isShowClockListView()) {
                        if (mClockHorizontalScrollView != null) {
                            SPUtils.getInstance().put(KEY_SUBSCREEN_CLOCK_SELECT, mClockHorizontalScrollView.getCurPage() - 1);
                            SecondaryScreenBitmapManager.getInstance().switchWatchStyle();
                        }
                        ClockListViewManager.getInstance().hideClockListView();
                    }
                });
                return super.onDoubleTap(e);
            }

            @Override
            public boolean onScroll(@Nullable MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
                return super.onScroll(e1, e2, distanceX, distanceY);
            }
        });
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "ClockListRelativeLayout onDestroy: ");
        removeAllViews();
        mClickViews.clear();
    }

    public interface OnClickListener {
        void onClick(int id);
    }

}