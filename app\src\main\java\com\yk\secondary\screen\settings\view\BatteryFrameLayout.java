package com.yk.secondary.screen.settings.view;

import static android.view.MotionEvent.ACTION_CANCEL;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.manager.BatteryViewManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;
import com.yk.secondary.screen.settings.utils.Constant;

public class BatteryFrameLayout extends FrameLayout {

    public BatteryFrameLayout(@NonNull Context context) {
        super(context);
        initDoubleTapListener(context);
    }

    public BatteryFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initDoubleTapListener(context);
    }

    public BatteryFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initDoubleTapListener(context);
    }

    private int mStartYPos;

    private MainHorizontalScrollView.OnDownSlideListener mOnDownSlideListener;

    private GestureDetector mGestureDetector;

    private boolean isDownSlide = false;
    private boolean isUpSlide = false;

    public void setOnSlideListener(MainHorizontalScrollView.OnDownSlideListener onDownSlideListener) {
        this.mOnDownSlideListener = onDownSlideListener;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mGestureDetector != null) {
            mGestureDetector.onTouchEvent(event);
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                Log.i(TAG_LOG, "BatteryFrameLayout onTouchEvent: ");
                SecondaryScreenControlManager.getInstance().removeSecondaryScreenSleepMsg();
                BatteryViewManager.getInstance().removeGoHomeTimeMsg();
                mStartYPos = (int) event.getY();
                break;
            case MotionEvent.ACTION_UP:
            case ACTION_CANCEL: {
                isDownSlide = false;
                isUpSlide = false;
                BatteryViewManager.getInstance().sendGoHomeTimeMsg();
                SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
                if (Math.abs((event.getY() - mStartYPos)) > (float) Constant.SCREEN_HEIGHT / 12) {
                    if (event.getY() - mStartYPos < 0) {
                        if (mOnDownSlideListener != null) {
                            BatteryViewManager.getInstance().setBottomMargin(Constant.SCREEN_HEIGHT);
                            mOnDownSlideListener.onDownSlide();
                        }
                    }
                } else {
                    if (mOnDownSlideListener != null) {
                        BatteryViewManager.getInstance().setBottomMargin(0);
                        mOnDownSlideListener.onDownSlide();
                    }
                }
            }
        }
        return true;
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {

            @Override
            public boolean onScroll(@Nullable MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
                if (!isDownSlide && !isUpSlide && distanceY < 0) {
                    isDownSlide = true;
                }

                if (!isUpSlide && !isDownSlide && distanceY > 0) {
                    isUpSlide = true;
                }

                if (isUpSlide && Math.abs(distanceX) < Math.abs(distanceY)) {
                    int bottomMargin = (int) (BatteryViewManager.getInstance().getBottomMargin() + distanceY);
                    if (bottomMargin <= 0) {
                        bottomMargin = 0;
                    }
                    BatteryViewManager.getInstance().setBottomMargin(bottomMargin);
                    if (mOnDownSlideListener != null) {
                        mOnDownSlideListener.onDownSlide();
                    }
                }
                return super.onScroll(e1, e2, distanceX, distanceY);
            }
        });
    }
}
