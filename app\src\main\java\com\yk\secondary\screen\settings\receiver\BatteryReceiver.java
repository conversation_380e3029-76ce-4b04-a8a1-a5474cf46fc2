package com.yk.secondary.screen.settings.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.BatteryManager;
import android.text.TextUtils;

import androidx.annotation.NonNull;


public class BatteryReceiver extends BroadcastReceiver {

    private BatteryStateChangeCallback mBatteryStateChangeCallback;

    public void setBatteryStateChangeCallback(@NonNull BatteryStateChangeCallback batteryStateChangeCallback) {
        mBatteryStateChangeCallback = batteryStateChangeCallback;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }

        String action = intent.getAction();
        if (TextUtils.equals(Intent.ACTION_BATTERY_CHANGED, action)) {
            int level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
            int scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 100);
            int mPluggedChargingSource = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);
            boolean mPluggedIn = mPluggedChargingSource != 0;
            final int status = intent.getIntExtra(BatteryManager.EXTRA_STATUS,
                    BatteryManager.BATTERY_STATUS_UNKNOWN);
            boolean mCharged = status == BatteryManager.BATTERY_STATUS_FULL;
            boolean mCharging = mCharged || status == BatteryManager.BATTERY_STATUS_CHARGING;
            int mCurrentLevel = (int) ((level / (float) scale) * 100);
            if (mBatteryStateChangeCallback != null) {
                mBatteryStateChangeCallback.onBatteryCurrentLevelChanged(mCurrentLevel, mPluggedIn, mCharging);
            }
        }
    }

    public interface BatteryStateChangeCallback {
        void onBatteryCurrentLevelChanged(int level, boolean pluggedIn, boolean charging);
    }
}
