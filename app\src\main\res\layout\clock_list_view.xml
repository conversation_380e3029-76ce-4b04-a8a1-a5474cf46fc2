<?xml version="1.0" encoding="utf-8"?>
<com.yk.secondary.screen.settings.view.ClockListRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/clock_list_layout"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@android:color/black"
    android:visibility="gone">

    <TextView
        android:id="@+id/clock_list_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginTop="10px"
        android:gravity="center"
        android:textColor="#ffffffff"
        android:textSize="18px" />

    <ImageView
        android:id="@+id/clock_list_prev"
        android:layout_width="35px"
        android:layout_height="35px"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:src="@drawable/comm_prev" />

    <ImageView
        android:id="@+id/clock_list_next"
        android:layout_width="35px"
        android:layout_height="35px"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/comm_next" />

    <ImageView
        android:id="@+id/clock_list_set"
        android:layout_width="35px"
        android:layout_height="35px"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:src="@drawable/comm_selected" />

    <com.yk.secondary.screen.settings.view.ClockHorizontalScrollView
        android:id="@+id/clock_scroll_view"
        android:layout_width="@dimen/clock_list_width"
        android:layout_height="@dimen/clock_list_height"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:fadingEdge="none"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/container_clock"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal" />

    </com.yk.secondary.screen.settings.view.ClockHorizontalScrollView>
</com.yk.secondary.screen.settings.view.ClockListRelativeLayout>
