<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@drawable/icon_watch_style9_bg"
    android:descendantFocusability="blocksDescendants">

    <ImageView
        android:id="@+id/iv_week"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="85px"
        android:layout_marginTop="74px" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="120px"
        android:layout_marginTop="74px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_month_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_month_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style9_date_tag" />

        <ImageView
            android:id="@+id/iv_day_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_day_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="38px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_time_h_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_time_h_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="1px"
            android:layout_marginEnd="1px"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style9_hour_min_tag" />

        <ImageView
            android:id="@+id/iv_time_m_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_time_m_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="1px"
            android:layout_marginEnd="1px"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style9_sec_tag" />

        <ImageView
            android:id="@+id/iv_time_sec_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style9_sec_0" />

        <ImageView
            android:id="@+id/iv_time_sec_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style9_sec_0" />
    </LinearLayout>

    <com.yk.secondary.screen.settings.view.WatchStyle9ClockView
        android:id="@+id/ac"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_width"
        android:layout_gravity="center"
        tools:ignore="MissingConstraints" />

</FrameLayout>