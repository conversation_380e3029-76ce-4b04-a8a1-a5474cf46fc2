package com.yk.secondary.screen.settings.view;

import static android.view.MotionEvent.ACTION_CANCEL;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_GOHOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.MSG_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.BatteryViewManager;
import com.yk.secondary.screen.settings.manager.CameraManager;
import com.yk.secondary.screen.settings.manager.ClockListViewManager;
import com.yk.secondary.screen.settings.manager.MusicViewManager;
import com.yk.secondary.screen.settings.manager.NotificationViewManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;
import com.yk.secondary.screen.settings.manager.WatchViewManager;
import com.yk.secondary.screen.settings.utils.Constant;
import com.yk.secondary.screen.settings.utils.SPUtils;

import java.util.HashMap;

public class MainHorizontalScrollView extends HorizontalScrollView {

    public static final int VIEW_MUSIC = 0;
    public static final int VIEW_WATCH = 1;
    public static final int VIEW_CAMERA = 2;

    private final HashMap<Integer, OnClickListener> mClickViews = new HashMap<>();

    public void setOnClickListener(@NonNull View view, OnClickListener listener) {
        mClickViews.put(view.getId(), listener);
    }

    private OnDownSlideListener mOnDownSlideListener;

    public void setOnSlideListener(OnDownSlideListener onDownSlideListener) {
        this.mOnDownSlideListener = onDownSlideListener;
    }

    private OnUpSlideListener mOnUpSlideListener;

    public void setOnUpSlideListener(OnUpSlideListener onUpSlideListener) {
        this.mOnUpSlideListener = onUpSlideListener;
    }

    private LinearLayout mChildGroup = null;

    private int mStartXPos, mStartYPos;

    private GestureDetector mGestureDetector;

    private int mCurViewPage = VIEW_WATCH;

    private boolean isDownSlide = false;
    private boolean isUpSlide = false;
    private boolean isUpDownSlide = false;//是否是上下滑动
    private boolean isLeftRightSlide = false;//是否左右滑动

    public int getCurViewPage() {
        return mCurViewPage;
    }

    public void setChildGroup(LinearLayout mChildGroup) {
        this.mChildGroup = mChildGroup;
    }

    public MainHorizontalScrollView(Context mContext, AttributeSet attrs, int defStyle) {
        super(mContext, attrs, defStyle);
        initDoubleTapListener(mContext);
    }

    public MainHorizontalScrollView(Context mContext, AttributeSet attrs) {
        super(mContext, attrs);
        initDoubleTapListener(mContext);
    }

    public MainHorizontalScrollView(Context mContext) {
        super(mContext);
        initDoubleTapListener(mContext);
    }

    private final Handler mGoHomeTimeHandler = new Handler(Looper.getMainLooper(), msg -> {
        if (msg.what == MSG_GO_HOME_TIME) {
            Log.i(TAG_LOG, "MainHorizontalScrollView : mGoHomeTimeHandler MSG_GO_HOME_TIME .... ");
            //显示表盘
            showWatchView();
        }
        return false;
    });

    public void showWatchView() {
        if (mCurViewPage == VIEW_WATCH) {
            return;
        }

        if (mCurViewPage == VIEW_CAMERA) {
            prevPage();
            return;
        }

        if (mCurViewPage == VIEW_MUSIC) {
            nextPage();
        }
    }

    public void sendGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
        int time = SPUtils.getInstance().getInt(KEY_SUBSCREEN_GOHOME_TIME, DEFAULT_GO_HOME_TIME);
        if (time != -1) {
            mGoHomeTimeHandler.sendEmptyMessageDelayed(MSG_GO_HOME_TIME, time);
        }
    }

    public void removeGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (mGestureDetector != null) {
            mGestureDetector.onTouchEvent(ev);
        }

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                removeGoHomeTimeMsg();
                Log.i(TAG_LOG, "MainHorizontalScrollView onTouchEvent: ");
                SecondaryScreenControlManager.getInstance().removeSecondaryScreenSleepMsg();
                mStartXPos = (int) ev.getX();
                mStartYPos = (int) ev.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                if (isUpDownSlide) {
                    return true;
                }

                int deltaX = Math.abs((int) (ev.getX() - mStartXPos));
                int deltaY = Math.abs((int) (ev.getY() - mStartYPos));

                // 滑动阈值
                final int MIN_DELTA = Constant.SCREEN_HEIGHT / 30;

                // 判断是否为上下滑（排除斜滑）
                if (!isLeftRightSlide && deltaY > MIN_DELTA && deltaY > deltaX * 1.5) {
                    isUpDownSlide = true;
                    return true;
                }

                if (!isLeftRightSlide && ev.getX() != mStartXPos && !isUpDownSlide) {
                    isLeftRightSlide = true;
                }
                break;
            case MotionEvent.ACTION_UP:
            case ACTION_CANCEL: {
                sendGoHomeTimeMsg();
                SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
                if (isLeftRightSlide) {
                    if (Math.abs((ev.getX() - mStartXPos)) > (float) Constant.SCREEN_WIDTH / 12) {
                        if (ev.getX() - mStartXPos > 0) {
                            prevPage();
                        } else {
                            nextPage();
                        }
                    } else {
                        this.scrollTo(240, 0);
                    }
                }
                if (isUpDownSlide) {
                    if (Math.abs((ev.getY() - mStartYPos)) > (float) Constant.SCREEN_HEIGHT / 12) {
                        if (ev.getY() - mStartYPos > 0) {
                            if (mOnDownSlideListener != null) {
                                BatteryViewManager.getInstance().setBottomMargin(0);
                                mOnDownSlideListener.onDownSlide();
                            }
                        } else {
                            if (mOnUpSlideListener != null) {
                                NotificationViewManager.getInstance().setTopMargin(0);
                                mOnUpSlideListener.onUpSlide();
                            }
                        }
                    } else {
                        if (mOnDownSlideListener != null) {
                            BatteryViewManager.getInstance().setBottomMargin(Constant.SCREEN_HEIGHT);
                            mOnDownSlideListener.onDownSlide();
                        }

                        if (mOnUpSlideListener != null) {
                            NotificationViewManager.getInstance().setTopMargin(Constant.SCREEN_HEIGHT);
                            mOnUpSlideListener.onUpSlide();
                        }
                    }
                }

                mStartXPos = 0;
                mStartYPos = 0;
                isDownSlide = false;
                isUpSlide = false;
                isUpDownSlide = false;
                isLeftRightSlide = false;
                return true;
            }
        }

        if (BatteryViewManager.getInstance().getBottomMargin() < Constant.SCREEN_HEIGHT || NotificationViewManager.getInstance().getTopMargin() < Constant.SCREEN_HEIGHT) {
            //说明电量界面开始显示了，禁止左右滑动
            return true;
        }
        return true;
        //return super.onTouchEvent(ev);
    }

    public void init() {
        if (mChildGroup == null) {
            return;
        }
        mChildGroup.removeAllViews();
        addRight(createMusicView());
        addRight(createWatchView());
        addRight(createCameraIconView());
        mChildGroup.addOnLayoutChangeListener(new OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                mChildGroup.removeOnLayoutChangeListener(this);
                scrollTo(240, 0);
            }
        });
        sendGoHomeTimeMsg();
    }

    public synchronized void prevPage() {
        View view = removeRight();
        addLeft(view);
        mCurViewPage = computationPreCurViewPage(mCurViewPage);
        Log.i(TAG_LOG, "MainHorizontalScrollView prevPage: mCurViewPage " + getCurViewPageName());
    }

    public synchronized void nextPage() {
        View view = removeLeft();
        addRight(view);
        mCurViewPage = computationNextCurViewPage(mCurViewPage);
        Log.i(TAG_LOG, "MainHorizontalScrollView nextPage: mCurViewPage " + getCurViewPageName());
    }

    /**
     * 向右边添加View
     *
     * @param view 需要添加的View
     */
    public void addRight(View view) {
        if (view == null || mChildGroup == null) return;
        // 添加安全检查，确保视图没有父视图或先移除
        if (view.getParent() != null) {
            ((ViewGroup)view.getParent()).removeView(view);
        }
        mChildGroup.addView(view);
        this.scrollTo(240, 0);
    }

    /**
     * 删除右边的View
     */
    public View removeRight() {
        if (mChildGroup == null || mChildGroup.getChildCount() <= 0) return null;
        View view = mChildGroup.getChildAt(mChildGroup.getChildCount() - 1);
        mChildGroup.removeView(view);
        return view;
    }

    /**
     * 向左边添加View
     *
     * @param view 需要添加的View
     */
    public void addLeft(View view) {
        if (view == null || mChildGroup == null) return;
        mChildGroup.addView(view, 0);
        this.scrollTo(240, 0);
    }

    /**
     * 删除左边的View
     */
    public View removeLeft() {
        if (mChildGroup == null || mChildGroup.getChildCount() <= 0) return null;
        /*因为在左边删除了View，因此所有View的x坐标都会减少，因此需要让ScrollView也跟着移动。*/
        View view = mChildGroup.getChildAt(0);
        mChildGroup.removeView(view);
        return view;
    }

    private View createMusicView() {
        MusicViewManager.getInstance().setYkHorizontalScrollView(this);
        View view = MusicViewManager.getInstance().createMusicView();
        //视图大小
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
        view.setLayoutParams(layoutParams);
        return view;
    }

    private View createWatchView() {
        View view = WatchViewManager.getInstance().createWatchView();
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
        view.setLayoutParams(layoutParams);
        return view;
    }

    private View createCameraIconView() {
        CameraManager.getInstance().setYkHorizontalScrollView(this);
        View view = CameraManager.getInstance().createCameraIconView();
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
        view.setLayoutParams(layoutParams);
        return view;
    }

    private int computationPreCurViewPage(int viewPage) {
        switch (viewPage) {
            case VIEW_MUSIC:
                return VIEW_CAMERA;
            case VIEW_WATCH:
                return VIEW_MUSIC;
            case VIEW_CAMERA:
                return VIEW_WATCH;
        }
        return VIEW_WATCH;
    }

    private int computationNextCurViewPage(int viewPage) {
        switch (viewPage) {
            case VIEW_MUSIC:
                return VIEW_WATCH;
            case VIEW_WATCH:
                return VIEW_CAMERA;
            case VIEW_CAMERA:
                return VIEW_MUSIC;
        }
        return VIEW_WATCH;
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {

            @Override
            public boolean onSingleTapUp(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "MainHorizontalScrollView onSingleTapUp: " + getCurViewPageName());
                try {
                    float x = e.getX();
                    float y = e.getY();
                    if (mClickViews.isEmpty()) {
                        return super.onSingleTapUp(e);
                    }
                    if (mCurViewPage == VIEW_MUSIC) {
                        mClickViews.forEach((id, listener) -> {
                            if (id == R.id.ib_play_pause) {//音乐界面播放按钮
                                if (x >= 86 && x <= 154 && y >= 108 && y <= 176) {
                                    listener.onClick(id);
                                }
                            } else if (id == R.id.ib_play_pre) {//音乐界面上一曲
                                if (x >= 0 && x <= 68 && y >= 108 && y <= 176) {
                                    listener.onClick(id);
                                }
                            } else if (id == R.id.ib_play_next) {//音乐界面下一曲
                                if (x >= 172 && x <= 240 && y >= 108 && y <= 176) {
                                    listener.onClick(id);
                                }
                            }

                        });
                    } else if (mCurViewPage == VIEW_CAMERA) {
                        mClickViews.forEach((id, listener) -> {
                            if (id == R.id.fl_camera_icon) {
                                listener.onClick(id);
                            }
                        });
                    }
                } catch (Exception exception) {
                    Log.i(TAG_LOG, "MainHorizontalScrollView onSingleTapUp: exception " + exception.getMessage());
                }
                return super.onSingleTapUp(e);
            }

            @Override
            public boolean onDoubleTap(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "MainHorizontalScrollView onDoubleTap: ");
                if (mCurViewPage == VIEW_WATCH) {
                    if (!ClockListViewManager.getInstance().isShowClockListView()) {
                        ClockListViewManager.getInstance().showClockListView();
                    }
                }
                return super.onDoubleTap(e);
            }

            @Override
            public boolean onScroll(@Nullable MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
                if (isUpDownSlide && !isDownSlide && !isUpSlide && distanceY < 0) {
                    isDownSlide = true;
                }

                if (isUpDownSlide && !isUpSlide && !isDownSlide && distanceY > 0) {
                    isUpSlide = true;
                }
                if (Math.abs(distanceX) < Math.abs(distanceY)) {
                    if (isDownSlide && mOnDownSlideListener != null) {
                        BatteryViewManager.getInstance().setBottomMargin(BatteryViewManager.getInstance().getBottomMargin() + distanceY);
                        mOnDownSlideListener.onDownSlide();
                    }

                    if (isUpSlide && mOnUpSlideListener != null) {
                        NotificationViewManager.getInstance().setTopMargin(NotificationViewManager.getInstance().getTopMargin() - distanceY);
                        mOnUpSlideListener.onUpSlide();
                    }
                }
                return super.onScroll(e1, e2, distanceX, distanceY);
            }
        });
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "MainHorizontalScrollView onDestroy: ");
        mStartYPos = 0;
        mStartXPos = 0;
        removeAllViews();
        WatchViewManager.getInstance().onDestroy();
        MusicViewManager.getInstance().onDestroy();
        CameraManager.getInstance().onIconViewDestroy();
        mClickViews.clear();

        mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        mGoHomeTimeHandler.removeCallbacksAndMessages(null);
    }

    private String getCurViewPageName() {
        switch (mCurViewPage) {
            case VIEW_MUSIC:
                return "音乐";
            case VIEW_WATCH:
                return "表盘";
            case VIEW_CAMERA:
                return "相机";
        }
        return "未知";
    }

    public interface OnClickListener {
        void onClick(int id);
    }

    public interface OnDownSlideListener {
        void onDownSlide();
    }

    public interface OnUpSlideListener {
        void onUpSlide();
    }
}