package com.yk.secondary.screen.settings.utils;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.drawable.StateListDrawable;
import android.graphics.drawable.VectorDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class ImageUtils {

    // 假设context是当前的Context，vectorResId是VectorDrawable资源的ID，width和height是转换后的Bitmap的尺寸
    public static Bitmap vectorToBitmap(VectorDrawable drawable, int width, int height) {
        // 获取VectorDrawable对象
        // 创建Bitmap对象
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        // 创建Canvas对象并将其与Bitmap关联
        Canvas canvas = new Canvas(bitmap);
        // 设置VectorDrawable的大小
        drawable.setBounds(0, 0, width, height);
        // 将VectorDrawable绘制在Canvas上
        drawable.draw(canvas);
        return bitmap;
    }

    public static byte[] convertBitmapToRGB565(Bitmap bitmap) {
        // 创建一个ByteBuffer用于存储像素数据
        ByteBuffer byteBuffer = ByteBuffer.allocate(bitmap.getWidth() * bitmap.getHeight() * 2);
        // 设置字节序为小端
        byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
        // 将Bitmap的像素数据复制到ByteBuffer中
        bitmap.copyPixelsToBuffer(byteBuffer);
        // 转换为字节数组
        // 如果需要，可以对pixels数组进行处理，例如裁剪或者转换格式
        return byteBuffer.array();
    }

    /**
     * 通过canvas复制view的bitmap
     */
    public static Bitmap copyByCanvas(View view) {
        try {
            view.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
            int width = view.getMeasuredWidth();
            int height = view.getMeasuredHeight();
            view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
            Bitmap bp = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
            Canvas canvas = new Canvas(bp);
            view.draw(canvas);
            canvas.save();
            return bp;
        } catch (Exception e) {
            Log.e(TAG_LOG, "copyByCanvas: Exception " + e.getMessage());
            return null;
        }
    }

    public static Bitmap createBitmapByView(View view) {
        try {
            //测量使得view指定大小
            int measureWidth = View.MeasureSpec.makeMeasureSpec(Constant.SCREEN_WIDTH, View.MeasureSpec.EXACTLY);
            int measureHeight = View.MeasureSpec.makeMeasureSpec(Constant.SCREEN_HEIGHT, View.MeasureSpec.AT_MOST);
            view.measure(measureWidth, measureHeight);
            //调用layout方法布局后，可以得到view的尺寸
            view.layout(0, 0, Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
            final Bitmap bitmap = Bitmap.createBitmap(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT, Bitmap.Config.RGB_565);
            Canvas canvas = new Canvas(bitmap);
            view.draw(canvas);
            return bitmap;
        } catch (Exception e) {
            Log.e(TAG_LOG, "createBitmapByView: Exception " + e.getMessage());
        }
        return null;
    }

    public static Bitmap clip(final Bitmap src,
                              final int x,
                              final int y,
                              final int width,
                              final int height,
                              final boolean recycle) {
        if (isEmptyBitmap(src)) return null;
        Bitmap ret = Bitmap.createBitmap(src, x, y, width, height);
        if (recycle && !src.isRecycled() && ret != src) src.recycle();
        return ret;
    }

    public static Bitmap convertToLcd(Bitmap bitmap) {
        // 创建一个新的Bitmap，其大小与原始位图相同
        Bitmap lcdBitmap = Bitmap.createBitmap(bitmap.getWidth(), bitmap.getHeight(), Bitmap.Config.ARGB_8888);
        // 为新位图创建Canvas
        Canvas canvas = new Canvas(lcdBitmap);
        // 创建画笔，用于绘制LCD样式
        Paint paint = new Paint();
        paint.setAntiAlias(true); // 抗锯齿
        // 将原始Bitmap绘制到Canvas上，模拟LCD效果
        canvas.drawBitmap(bitmap, 0, 0, paint);
        // 返回转换后的LCD位图
        return lcdBitmap;
    }

    public static Bitmap convertToLcdFormat(Bitmap bitmap) {
        // 创建一个新的Bitmap，像素格式为RGB_565，以节省内存
        return bitmap.copy(Bitmap.Config.RGB_565, false);
    }

    /**
     * Bitmap to bytes.
     *
     * @param bitmap The bitmap.
     * @return bytes
     */
    public static byte[] bitmap2Bytes(final Bitmap bitmap) {
        return bitmap2Bytes(bitmap, Bitmap.CompressFormat.PNG, 100);
    }

    /**
     * Bitmap to bytes.
     *
     * @param bitmap  The bitmap.
     * @param format  The format of bitmap.
     * @param quality The quality.
     * @return bytes
     */
    public static byte[] bitmap2Bytes(@Nullable final Bitmap bitmap, @NonNull final Bitmap.CompressFormat format, int quality) {
        if (bitmap == null) return null;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(format, quality, baos);
        return baos.toByteArray();
    }

    /**
     * Save the bitmap.
     *
     * @param src      The source of bitmap.
     * @param filePath The path of file.
     * @param format   The format of the image.
     * @return {@code true}: success<br>{@code false}: fail
     */
    public static boolean save(final Bitmap src,
                               final String filePath,
                               final Bitmap.CompressFormat format) {
        return save(src, filePath, format, 100, false);
    }

    /**
     * Save the bitmap.
     *
     * @param src      The source of bitmap.
     * @param filePath The path of file.
     * @param format   The format of the image.
     * @param quality  Hint to the compressor, 0-100. 0 meaning compress for
     *                 small size, 100 meaning compress for max quality. Some
     *                 formats, like PNG which is lossless, will ignore the
     *                 quality setting
     * @param recycle  True to recycle the source of bitmap, false otherwise.
     * @return {@code true}: success<br>{@code false}: fail
     */
    public static boolean save(final Bitmap src,
                               final String filePath,
                               final Bitmap.CompressFormat format,
                               final int quality,
                               final boolean recycle) {
        return save(src, FileUtils.getFileByPath(filePath), format, quality, recycle);
    }


    /**
     * Save the bitmap.
     *
     * @param src     The source of bitmap.
     * @param file    The file.
     * @param format  The format of the image.
     * @param quality Hint to the compressor, 0-100. 0 meaning compress for
     *                small size, 100 meaning compress for max quality. Some
     *                formats, like PNG which is lossless, will ignore the
     *                quality setting
     * @param recycle True to recycle the source of bitmap, false otherwise.
     * @return {@code true}: success<br>{@code false}: fail
     */
    public static boolean save(final Bitmap src,
                               final File file,
                               final Bitmap.CompressFormat format,
                               final int quality,
                               final boolean recycle) {
        if (isEmptyBitmap(src)) {
            Log.e("ImageUtils", "bitmap is empty.");
            return false;
        }
        if (src.isRecycled()) {
            Log.e("ImageUtils", "bitmap is recycled.");
            return false;
        }
        if (!FileUtils.createFileByDeleteOldFile(file)) {
            Log.e("ImageUtils", "create or delete file <" + file + "> failed.");
            return false;
        }
        OutputStream os = null;
        boolean ret = false;
        try {
            os = new BufferedOutputStream(new FileOutputStream(file));
            ret = src.compress(format, quality, os);
            if (recycle && !src.isRecycled()) src.recycle();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return ret;
    }

    private static boolean isEmptyBitmap(final Bitmap src) {
        return src == null || src.getWidth() == 0 || src.getHeight() == 0;
    }

    public static Bitmap mirror(final Bitmap src,
                                final boolean recycle) {
        if (isEmptyBitmap(src)) return null;
        Matrix matrix = new Matrix();
        matrix.postScale(-1, 1, src.getWidth() / 2, src.getHeight() / 2);
        Bitmap ret = Bitmap.createBitmap(src, 0, 0, src.getWidth(), src.getHeight(), matrix, true);
        if (recycle && !src.isRecycled() && ret != src) src.recycle();
        return ret;
    }

    public static Bitmap getBitmap(final String filePath) {
        if (TextUtils.isEmpty(filePath)) return null;
        return BitmapFactory.decodeFile(filePath);
    }


    public static Bitmap rotateAndMirror(final Bitmap src,
                                         final int degrees,
                                         final float px,
                                         final float py,
                                         final boolean recycle) {
        if (isEmptyBitmap(src)) return null;
        if (degrees == 0) return src;
        Matrix matrix = new Matrix();
        matrix.setRotate(degrees, px, py);
        matrix.postScale(-1, 1, (float) src.getWidth() / 2, 0);
        Bitmap ret = Bitmap.createBitmap(src, 0, 0, src.getWidth(), src.getHeight(), matrix, true);
        if (recycle && !src.isRecycled() && ret != src) src.recycle();
        return ret;
    }

    public static Bitmap rotate(final Bitmap src,
                                final int degrees,
                                final float px,
                                final float py,
                                final boolean recycle) {
        if (isEmptyBitmap(src)) return null;
        if (degrees == 0) return src;
        Matrix matrix = new Matrix();
        matrix.setRotate(degrees, px, py);
        Bitmap ret = Bitmap.createBitmap(src, 0, 0, src.getWidth(), src.getHeight(), matrix, true);
        if (recycle && !src.isRecycled() && ret != src) src.recycle();
        return ret;
    }

    public static Bitmap scale(final Bitmap src,
                               final int newWidth,
                               final int newHeight,
                               final boolean recycle) {
        if (isEmptyBitmap(src)) return null;
        Bitmap ret = Bitmap.createScaledBitmap(src, newWidth, newHeight, true);
        if (recycle && !src.isRecycled() && ret != src) src.recycle();
        return ret;
    }

    public static Bitmap stateListDrawableToBitmap(StateListDrawable stateListDrawable) {
        // 创建一个和StateListDrawable一样大小的空Bitmap
        Bitmap bitmap = Bitmap.createBitmap(stateListDrawable.getIntrinsicWidth(),
                stateListDrawable.getIntrinsicHeight(),
                Bitmap.Config.ARGB_8888);
        // 使用上述Bitmap创建一个Canvas
        Canvas canvas = new Canvas(bitmap);
        // 将StateListDrawable绘制在Canvas上
        stateListDrawable.setBounds(0, 0, stateListDrawable.getIntrinsicWidth(), stateListDrawable.getIntrinsicHeight());
        stateListDrawable.draw(canvas);
        return bitmap;
    }
}
