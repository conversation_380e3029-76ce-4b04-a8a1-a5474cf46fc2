package com.yk.secondary.screen.settings.view.watch;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.util.Log;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.TimeUtils;

public class WatchStyle6 extends BaseWatchStyle {

    private ImageView mIvTimeH1, mIvTimeH2, mIvTimeM1, mIvTimeM2;

    public WatchStyle6() {
        super();
    }

    @Override
    public void initViews() {
        mRootView = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_bitmap_watch_style6, null);

        mIvTimeH1 = mRootView.findViewById(R.id.iv_time_h_1);
        mIvTimeH2 = mRootView.findViewById(R.id.iv_time_h_2);
        setTimeH();

        mIvTimeM1 = mRootView.findViewById(R.id.iv_time_m_1);
        mIvTimeM2 = mRootView.findViewById(R.id.iv_time_m_2);
        setTimeM();
    }

    @Override
    public int getStyleValue() {
        return 6;
    }

    @Override
    public void updateTimeChanged() {
        setTimeH();
        setTimeM();
    }

    @Override
    public void updateDateChanged() {
        setTimeH();
        setTimeM();
    }

    @Override
    public void updateCurrentLevelChanged(BatteryState batteryState) {

    }

    private void setTimeH() {
        String h = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH"));
        try {
            if (mIvTimeH1 != null) {
                mIvTimeH1.setImageResource(getHourImageResource(h.charAt(0)));
            }

            if (mIvTimeH2 != null) {
                mIvTimeH2.setImageResource(getHourImageResource(h.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeH: Exception " + e.getMessage());
        }
    }

    private void setTimeM() {
        String m = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("mm"));
        try {
            if (mIvTimeM1 != null) {
                mIvTimeM1.setImageResource(getMinImageResource(m.charAt(0)));
            }

            if (mIvTimeM2 != null) {
                mIvTimeM2.setImageResource(getMinImageResource(m.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setTimeM: Exception " + e.getMessage());
        }
    }

    private int getHourImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style6_hour_0;
            case '1':
                return R.drawable.icon_watch_style6_hour_1;
            case '2':
                return R.drawable.icon_watch_style6_hour_2;
            case '3':
                return R.drawable.icon_watch_style6_hour_3;
            case '4':
                return R.drawable.icon_watch_style6_hour_4;
            case '5':
                return R.drawable.icon_watch_style6_hour_5;
            case '6':
                return R.drawable.icon_watch_style6_hour_6;
            case '7':
                return R.drawable.icon_watch_style6_hour_7;
            case '8':
                return R.drawable.icon_watch_style6_hour_8;
            case '9':
                return R.drawable.icon_watch_style6_hour_9;
            default:
                return R.drawable.icon_watch_style6_hour_0;
        }
    }

    private int getMinImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style6_min_0;
            case '1':
                return R.drawable.icon_watch_style6_min_1;
            case '2':
                return R.drawable.icon_watch_style6_min_2;
            case '3':
                return R.drawable.icon_watch_style6_min_3;
            case '4':
                return R.drawable.icon_watch_style6_min_4;
            case '5':
                return R.drawable.icon_watch_style6_min_5;
            case '6':
                return R.drawable.icon_watch_style6_min_6;
            case '7':
                return R.drawable.icon_watch_style6_min_7;
            case '8':
                return R.drawable.icon_watch_style6_min_8;
            case '9':
                return R.drawable.icon_watch_style6_min_9;
            default:
                return R.drawable.icon_watch_style6_min_0;
        }
    }
}
