<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    android:title="@string/subscreen_setting">
    <SwitchPreference
        android:key="miniscreen_main_switch"
        android:title="@string/app_name" />
    <PreferenceCategory
        android:key="subscreen_display_setting"
        android:title="@string/display_settings">
        <Preference
            android:key="subscreen_clock_select"
            android:title="@string/subscreen_clock_select" />
<!--        <Preference
            android:key="subscreen_compass_select"
            android:title="@string/subscreen_compass_select" />-->
        <ListPreference
            android:entries="@array/screen_timeout_entries"
            android:entryValues="@array/screen_timeout_values"
            android:key="subscreen_screen_timeout"
            android:persistent="false"
            android:summary="@string/screen_timeout_30_sec"
            android:title="@string/screen_timeout" />
        <ListPreference
            android:entries="@array/backlight_level_entries"
            android:entryValues="@array/backlight_level_values"
            android:key="subscreen_backlight_level"
            android:persistent="false"
            android:summary="@string/backlight_level_8"
            android:title="@string/backlight_level" />
<!--        <ListPreference
            android:entries="@array/eye_protect_level_array"
            android:entryValues="@array/eye_protect_level_array_entryvalues"
            android:key="eye_protect_mode_level"
            android:persistent="false"
            android:summary="@string/eye_protect_level_no"
            android:title="@string/eye_protect_picker_title" />-->
        <ListPreference
            android:entries="@array/screen_gohome_entries"
            android:entryValues="@array/screen_gohome_values"
            android:key="subscreen_gohome_time"
            android:persistent="false"
            android:summary="@string/screen_timeout_never"
            android:title="@string/subscreen_gohome_time" />
  <!--      <Preference
            android:key="subscreen_stepcount"
            android:summary="@string/subscreen_stepcount_summary_off"
            android:title="@string/subscreen_stepcount" />-->
        <!--<SwitchPreference
            android:key="subscreen_aod_enable"
            android:summary="@string/subscreen_aod_summary"
            android:title="@string/subscreen_aod_title" />-->
    </PreferenceCategory>
    <PreferenceCategory
        android:key="screenon_setting"
        android:title="@string/screenon_setting">
        <SwitchPreference
            android:key="subscreen_with_mainscreen"
            android:summary="@string/screenon_with_mainscreen_summary"
            android:title="@string/screenon_with_mainscreen_title" />
<!--        <SwitchPreference
            android:key="subscreen_noti_screenon"
            android:summary="@string/screenon_noti_summary"
            android:title="@string/screenon_noti_title" />-->
        <SwitchPreference
            android:key="subscreen_flip_screenon"
            android:summary="@string/screenon_flip_summary"
            android:title="@string/screenon_flip_title" />
        <SwitchPreference
            android:key="double_touch_key"
            android:summary="@string/double_touch_summary"
            android:title="@string/double_touch_title" />
    </PreferenceCategory>
    <PreferenceCategory
        android:key="subscreen_call_setting"
        android:title="@string/subscreen_call_setting">
        <SwitchPreference
            android:key="subscreen_call_tip_enable"
            android:summary="@string/subscreen_call_show_summary"
            android:title="@string/subscreen_call_show_title" />
    </PreferenceCategory>
    <PreferenceCategory
        android:key="subscreen_noti_setting"
        android:title="@string/subscreen_noti_setting">
        <SwitchPreference
            android:key="subscreen_noti_tip_enable"
            android:summary="@string/subscreen_noti_tip_summary"
            android:title="@string/subscreen_noti_tip_titile" />
        <Preference
            android:key="show_noti_apps"
            android:summary="@string/subscreen_show_noti_apps_summary"
            android:title="@string/subscreen_show_noti_apps_title" />
    </PreferenceCategory>
    <PreferenceCategory
        android:key="subscreen_music_setting"
        android:title="@string/subscreen_music_setting">
        <SwitchPreference
            android:key="open_music_app_enable"
            android:summary="@string/subscreen_open_music_app_summary"
            android:title="@string/subscreen_open_music_app_titile" />
        <Preference
            android:key="music_app_picker"
            android:title="@string/subscreen_pick_music_app" />
    </PreferenceCategory>
<!--    <PreferenceCategory
        android:key="subscreen_racetext_setting"
        android:title="@string/title_activity_race_text_settings">
        <SwitchPreference
            android:key="open_race_text_enable"
            android:summary="@string/subscreen_open_race_text_settings_summary"
            android:title="@string/subscreen_open_race_text_settings_titile" />
        <Preference
            android:key="racetext_setting_set"
            android:title="@string/subscreen_set_race_text_settings_titile" />
    </PreferenceCategory>-->
    <PreferenceCategory
        android:key="subscreen_camera_setting"
        android:title="@string/subscreen_camera_setting">
        <SwitchPreference
            android:key="subscreen_take_photo_voice_enable"
            android:summary="@string/subscreen_take_photo_voice_summary"
            android:title="@string/subscreen_take_photo_voice_titile" />
        <ListPreference
            android:entries="@array/back_camera_photosize_entries"
            android:entryValues="@array/back_camera_photosize_values"
            android:key="back_camera_photosize_picker"
            android:persistent="false"
            android:title="@string/subscreen_back_camera_photo_size" />
        <ListPreference
            android:entries="@array/front_camera_photosize_entries"
            android:entryValues="@array/front_camera_photosize_values"
            android:key="front_camera_photosize_picker"
            android:persistent="false"
            android:title="@string/subscreen_front_camera_photo_size" />
    </PreferenceCategory>
<!--    <PreferenceCategory
        android:key="miniscreen_help_setting"
        android:title="@string/miniscreen_help_setting">
        <Preference
            android:key="miniscreen_help_clock_select"
            android:title="@string/miniscreen_help_clock_select" />
        <Preference
            android:key="miniscreen_help_noti"
            android:title="@string/miniscreen_help_clock_noti" />
        <Preference
            android:key="miniscreen_help_status"
            android:title="@string/miniscreen_help_clock_status" />
        <Preference
            android:key="miniscreen_help_camera"
            android:title="@string/miniscreen_help_clock_camera" />
    </PreferenceCategory>-->
</PreferenceScreen>
