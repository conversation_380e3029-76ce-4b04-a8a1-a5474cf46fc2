<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="com.google.android.deskclock.permission.RECEIVE_ALERT_BROADCASTS" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND" />
    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission
        android:name="android.permission.WRITE_SMS"
        android:protectionLevel="signature|privileged" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_MMS" />
    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
    <uses-permission android:name="android.permission.ACCESS_MESSAGES_ON_ICC" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />

    <uses-feature android:name="android.hardware.camera" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SecondaryScreenSettings">

        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.INFO" />
            </intent-filter>
            <intent-filter android:priority="5">
                <action android:name="com.android.settings.action.EXTRA_SETTINGS" />
            </intent-filter>

            <meta-data
                android:name="com.android.settings.category"
                android:value="com.android.settings.category.ia.homepage" />
            <meta-data
                android:name="com.android.settings.icon"
                android:resource="@drawable/ic_settings_subscreen" />
            <!-- 菜单标题 -->
            <meta-data
                android:name="com.android.settings.title"
                android:value="@string/app_name" />
        </activity>

        <activity
            android:name=".ui.ClockSelectActivity"
            android:exported="false"
            android:label="@string/subscreen_clock_select"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings" />

        <activity
            android:name=".ui.MusicActivity"
            android:exported="false"
            android:label="@string/subscreen_clock_select"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings" />

        <activity
            android:name=".ui.NofiAppsSelectActivity"
            android:exported="false"
            android:label="@string/subscreen_show_noti_apps_title"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RBSettings" />

        <service
            android:name=".service.NotificationObserverService"
            android:excludeFromRecents="true"
            android:exported="false"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:stopWithTask="false">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>
        <service
            android:name=".factorytest.FactoryTestService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.yk.aidl.factory.test" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
    </application>

</manifest>