<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_camera_icon"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@drawable/icon_camera_bg">

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="20px"
        android:background="@drawable/shape_news_background"
        android:fontFamily="@font/pingfang_regular"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="4px"
        android:paddingEnd="4px"
        android:text="@string/camera_tip"
        android:textColor="#333333"
        android:textSize="20px"
        android:visibility="gone" />

</FrameLayout>