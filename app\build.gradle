plugins {
    alias(libs.plugins.android.application)
}

static def releaseTime() {
    return new Date().format("yyyy-MM-dd", TimeZone.getTimeZone("UTC"))
}

android {
    signingConfigs {
        release {
            keyAlias 'platform'
            keyPassword '8f7EsePz0p5sQlBbuZwv12Mc3T91BVW4oEHbVou5WWX0J5OCkfRumAt5JtB1OZTF'
            storeFile file('..\\sign\\release\\platform.keystore')
            storePassword '8f7EsePz0p5sQlBbuZwv12Mc3T91BVW4oEHbVou5WWX0J5OCkfRumAt5JtB1OZTF'
        }

        debug {
            keyAlias 'debug'
            keyPassword '8f7EsePz0p5sQlBbuZwv12Mc3T91BVW4oEHbVou5WWX0J5OCkfRumAt5JtB1OZTF'
            storeFile file('..\\sign\\debug\\debug.keystore')
            storePassword '8f7EsePz0p5sQlBbuZwv12Mc3T91BVW4oEHbVou5WWX0J5OCkfRumAt5JtB1OZTF'
        }
    }
    namespace 'com.yk.secondary.screen.settings'
    compileSdk 34

    defaultConfig {
        applicationId "com.yk.secondary.screen.settings"
        minSdk 34
        targetSdk 34
        versionCode 6
        versionName "1.6.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            // 不显示Log
            buildConfigField "boolean", "LOG_DEBUG", "false"
            //混淆
            minifyEnabled false
            // 移除无用的resource文件
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            buildConfigField "boolean", "LOG_DEBUG", "true"
            minifyEnabled false
            zipAlignEnabled false
            shrinkResources false
            signingConfig signingConfigs.debug
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    android.applicationVariants.configureEach { variant ->
        variant.outputs.configureEach {
            outputFileName = "SecondaryScreenSettings_V${defaultConfig.versionName}_${releaseTime()}_release.apk"
        }
    }

    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }
    buildFeatures {
        buildConfig = true
        aidl = true
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.jsr305
    implementation libs.google.guava
    implementation libs.github.glide
    annotationProcessor libs.glide.compiler

    implementation libs.camera.core
    implementation libs.androidx.camera.camera2
    implementation libs.androidx.camera.lifecycle
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.29'
}