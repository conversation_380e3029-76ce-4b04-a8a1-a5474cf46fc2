<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/noti_item_height">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/noti_item_height"
        android:layout_centerHorizontal="true">

        <RelativeLayout
            android:id="@+id/noti_title_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/noti_ic"
                android:layout_width="@dimen/noti_icon_size"
                android:layout_height="@dimen/noti_icon_size"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="30px" />

            <TextView
                android:id="@+id/noti_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/noti_ic"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10px"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="#ffffffff"
                android:textSize="@dimen/noti_title_text_size" />
        </RelativeLayout>

        <TextView
            android:id="@+id/noti_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/noti_title_layout"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/noti_content_text_margin_side"
            android:layout_marginTop="5dp"
            android:layout_marginRight="@dimen/noti_content_text_margin_side"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="#ffffffff"
            android:maxLines="4"
            android:textSize="@dimen/noti_content_text_size" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_alignParentBottom="true"
            android:background="#40e0d0" />
    </RelativeLayout>


</RelativeLayout>
