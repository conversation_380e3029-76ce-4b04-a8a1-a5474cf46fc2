package com.yk.secondary.screen.settings.factorytest;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.Rect;
import android.view.MotionEvent;
import android.view.View;

import com.yk.secondary.screen.settings.utils.Constant;

import java.util.ArrayList;
import java.util.List;

@SuppressLint("ViewConstructor")
public class LineTestView extends View {
    private final Paint mBgPaint;
    private final Rect mBgRect;
    private final Paint mRectPaint;
    private final Paint mStrokePaint;

    public List<RectItem> mAllRectItem = new ArrayList<>(0);
    public Canvas mCanvas;
    public Paint mTrackingPaint;
    private final Bitmap mBitmap;

    public LineTestView(Context context, int w, int h, List<RectItem> items) {
        super(context);
        mCanvas = new Canvas();
        mTrackingPaint = new Paint(Paint.DITHER_FLAG);
        mTrackingPaint.setAntiAlias(true);
        mTrackingPaint.setColor(Color.BLUE);
        mTrackingPaint.setStrokeCap(Paint.Cap.ROUND);
        mTrackingPaint.setStrokeWidth(2);

        mBitmap = Bitmap.createBitmap(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT, Bitmap.Config.ARGB_8888);
        mCanvas.setBitmap(mBitmap);

        mRectPaint = new Paint();
        mRectPaint.setAntiAlias(true);

        mBgPaint = new Paint();
        mBgPaint.setARGB(255, 0, 0, 0);
        mBgRect = new Rect(0, 0, w, h);

        mStrokePaint = new Paint();
        mStrokePaint.setARGB(255, 255, 255, 255);
        mStrokePaint.setStrokeWidth(1);

        mAllRectItem.addAll(items);
    }

    public void onDraw(Canvas canvas) {
        canvas.drawColor(Color.WHITE);
        canvas.drawRect(mBgRect, mBgPaint);
        mRectPaint.setARGB(255, 255, 0, 0);

        for (int i = 0; i < mAllRectItem.size(); i++) {
            RectItem myRectItem = mAllRectItem.get(i);
            if (myRectItem.isTested) {
                mRectPaint.setARGB(255, 0, 255, 0);
            } else {
                mRectPaint.setARGB(255, 255, 0, 0);
            }
            drawMyRect(canvas, myRectItem.rect);
        }
        mRectPaint.setARGB(255, 0, 0, 255);

        canvas.drawBitmap(mBitmap, 0, 0, null);
    }

    void drawMyRect(Canvas canvas, Rect rect) {
        final Point point1 = new Point(rect.left, rect.top);
        final Point point2 = new Point(rect.right, rect.top);
        final Point point3 = new Point(rect.left, rect.bottom);
        final Point point4 = new Point(rect.right, rect.bottom);

        canvas.drawRect(rect, mRectPaint);
        canvas.drawLine(point1.x, point1.y, point2.x, point2.y, mStrokePaint);
        canvas.drawLine(point1.x, point1.y, point3.x, point3.y, mStrokePaint);
        canvas.drawLine(point2.x, point2.y, point4.x, point4.y, mStrokePaint);
        canvas.drawLine(point3.x, point3.y, point4.x, point4.y, mStrokePaint);
    }

    private void CheckPoint(Point start, Point end, List<RectItem> items) {
        final int size = items.size();

        for (int i = 0; i < size; i++) {
            final RectItem item = items.get(i);
            final Rect rect = item.rect;
            if (isLineIntersectRectangle(start, end, rect.left, rect.top, rect.right, rect.bottom)) {
                if (item.isTested)
                    continue;
                item.isTested = true;
            }
        }
    }

    private boolean isLineIntersectRectangle(Point start, Point end, int rectangleLeftTopX, int rectangleLeftTopY, int rectangleRightBottomX, int rectangleRightBottomY) {
        final int lineHeight = start.y - end.y;
        final int lineWidth = end.x - start.x;

        // 计算叉乘
        final int c = start.x * end.y - end.x * start.y;

        int i = lineHeight * rectangleLeftTopX + lineWidth * rectangleLeftTopY + c;
        if ((i >= 0 && lineHeight * rectangleRightBottomX + lineWidth * rectangleRightBottomY + c <= 0)
                || (i <= 0 && lineHeight * rectangleRightBottomX + lineWidth * rectangleRightBottomY + c >= 0)
                || (lineHeight * rectangleLeftTopX + lineWidth * rectangleRightBottomY + c >= 0 && lineHeight * rectangleRightBottomX + lineWidth * rectangleLeftTopY + c <= 0)
                || (lineHeight * rectangleLeftTopX + lineWidth * rectangleRightBottomY + c <= 0 && lineHeight * rectangleRightBottomX + lineWidth * rectangleLeftTopY + c >= 0)) {
            if (rectangleLeftTopX > rectangleRightBottomX) {
                int temp = rectangleLeftTopX;
                rectangleLeftTopX = rectangleRightBottomX;
                rectangleRightBottomX = temp;
            }
            if (rectangleLeftTopY < rectangleRightBottomY) {
                int temp = rectangleLeftTopY;
                rectangleLeftTopY = rectangleRightBottomY;
                rectangleRightBottomY = temp;
            }
            return (start.x >= rectangleLeftTopX || end.x >= rectangleLeftTopX)
                    && (start.x <= rectangleRightBottomX || end.x <= rectangleRightBottomX)
                    && (start.y <= rectangleLeftTopY || end.y <= rectangleLeftTopY)
                    && (start.y >= rectangleRightBottomY || end.y >= rectangleRightBottomY);
        } else {
            return false;
        }
    }

    int xStart = 0, yStart = 0;

    @SuppressLint("ClickableViewAccessibility")
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_UP:
                if (CheckResult()) {
                    if (null != mCb) mCb.testDone();
                }
                break;
            case MotionEvent.ACTION_MOVE:
                //step1:
                final int x = (int) event.getX();
                final int y = (int) event.getY();

                final Point start = new Point(xStart, yStart);
                final Point end = new Point(x, y);
                CheckPoint(start, end, mAllRectItem);
                //step2:
                mCanvas.drawLine(xStart, yStart, event.getX(), event.getY(), mTrackingPaint);
                xStart = (int) event.getX();
                yStart = (int) event.getY();
                break;
            case MotionEvent.ACTION_DOWN:
                xStart = (int) event.getX();
                yStart = (int) event.getY();
                break;
            default:
                break;
        }
        invalidate();
        return true;
    }

    boolean CheckResult() {
        for (RectItem item : mAllRectItem) {
            if (!item.isTested) {
                return false;
            }
        }
        return true;
    }

    private Callback mCb;

    public void addCallback(Callback cb) {
        mCb = cb;
    }

    public interface Callback {
        void testDone();
    }
}
