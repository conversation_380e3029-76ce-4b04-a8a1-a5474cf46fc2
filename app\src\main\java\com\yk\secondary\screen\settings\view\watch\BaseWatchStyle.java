package com.yk.secondary.screen.settings.view.watch;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.SystemClock;
import android.view.View;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.listener.TimeChangeCallback;
import com.yk.secondary.screen.settings.receiver.BatteryReceiver;
import com.yk.secondary.screen.settings.receiver.TimeChangeReceiver;
import com.yk.secondary.screen.settings.utils.ThreadUtils;

import java.util.Calendar;

public abstract class BaseWatchStyle {

    public View mRootView;

    private TimeChangeReceiver mTimeChangeReceiver;

    private BatteryReceiver mBatteryReceiver;

    public BaseWatchStyle() {
        onViewCreated();
    }

    public View getRootView() {
        return mRootView;
    }

    private ThreadUtils.SimpleTask<Void> mTimeTask;

    private boolean isStopTimeTask = false;

    private int mCurH, mCurM, mCurS;

    public void onViewCreated() {
        startTimeTask();
        registerReceiver();
        initViews();
        updateCurrentLevelChanged(getBatteryState());
    }

    public void onDestroy() {
        unregisterReceiver();
        stopTimeTask();
    }

    private void startTimeTask() {
        isStopTimeTask = false;
        mTimeTask = new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                while (!isStopTimeTask) {
                    Calendar mCalendar = Calendar.getInstance();
                    //获取当前小时数
                    int hours = mCalendar.get(Calendar.HOUR);
                    //获取当前分钟数
                    int minutes = mCalendar.get(Calendar.MINUTE);
                    //获取当前秒数
                    int seconds = mCalendar.get(Calendar.SECOND);
                    if (mCurH != hours || mCurM != minutes || mCurS != seconds) {
                        updateTimeChanged();
                        mCurH = hours;
                        mCurM = minutes;
                        mCurS = seconds;
                    }

                    SystemClock.sleep(100);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        };
        ThreadUtils.executeByCpu(mTimeTask, Thread.MAX_PRIORITY);
    }

    private void stopTimeTask() {
        isStopTimeTask = true;
        if (mTimeTask != null) {
            mTimeTask.cancel();
        }
    }

    private void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_TIME_TICK);
        filter.addAction(Intent.ACTION_DATE_CHANGED);
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED);
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        if (mTimeChangeReceiver == null) {
            mTimeChangeReceiver = new TimeChangeReceiver();
            mTimeChangeReceiver.setTimeChangeCallback(new TimeChangeCallback() {
                @Override
                public void onTimeChanged() {
                    updateTimeChanged();
                }

                @Override
                public void onDateChanged() {
                    updateDateChanged();
                    updateTimeChanged();
                }
            });

        }
        App.getInstance().getContext().registerReceiver(mTimeChangeReceiver, filter, Context.RECEIVER_EXPORTED);

        IntentFilter filter1 = new IntentFilter();
        filter1.addAction(Intent.ACTION_BATTERY_CHANGED);
        if (mBatteryReceiver == null) {
            mBatteryReceiver = new BatteryReceiver();
            mBatteryReceiver.setBatteryStateChangeCallback(new BatteryReceiver.BatteryStateChangeCallback() {
                @SuppressLint("SetTextI18n")
                @Override
                public void onBatteryCurrentLevelChanged(int level, boolean pluggedIn, boolean charging) {
                    updateCurrentLevelChanged(new BatteryState(level, charging));
                }
            });
        }
        App.getInstance().getContext().registerReceiver(mBatteryReceiver, filter1, Context.RECEIVER_EXPORTED);
    }

    private void unregisterReceiver() {
        if (mTimeChangeReceiver != null) {
            App.getInstance().getContext().unregisterReceiver(mTimeChangeReceiver);
            mTimeChangeReceiver = null;
        }

        if (mBatteryReceiver != null) {
            App.getInstance().getContext().unregisterReceiver(mBatteryReceiver);
            mBatteryReceiver = null;
        }
    }

    public BatteryState getBatteryState() {
        int batteryLevel;
        BatteryManager batteryManager = (BatteryManager) App.getInstance().getContext().getSystemService(Context.BATTERY_SERVICE);
        if (batteryManager != null) {
            batteryLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
            return new BatteryState(batteryLevel, batteryManager.isCharging());
        }
        return new BatteryState(20, false);
    }

    public abstract void initViews();

    public abstract int getStyleValue();

    public abstract void updateTimeChanged();

    public abstract void updateDateChanged();

    public abstract void updateCurrentLevelChanged(BatteryState batteryState);
}
