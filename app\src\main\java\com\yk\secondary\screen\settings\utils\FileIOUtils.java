package com.yk.secondary.screen.settings.utils;

import android.content.Context;
import android.util.Log;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;

public class FileIOUtils {

    private static int sBufferSize = 524288;

    public static String readFile2String(Context context, final String filePath) {
        return readFile2String(context, FileUtils.getFileByPath(filePath), null);
    }

    public static boolean writeFileFromString(final String filePath, final String content) {
        return writeFileFromString(FileUtils.getFileByPath(filePath), content, false);
    }

    private static String readFile2String(Context context, final File file, final String charsetName) {
        byte[] bytes = readFile2BytesByStream(context, file);
        if (bytes == null) return null;
        if (FileUtils.isSpace(charsetName)) {
            return new String(bytes);
        } else {
            try {
                return new String(bytes, charsetName);
            } catch (UnsupportedEncodingException e) {
                Log.e("FileIOUtils", "readFile2String: UnsupportedEncodingException " + e.getMessage());
                return "";
            }
        }
    }

    private static boolean writeFileFromString(final File file,
                                               final String content,
                                               final boolean append) {
        if (file == null || content == null) return false;
        if (!createOrExistsFile(file)) {
            Log.e("FileIOUtils", "create file <" + file + "> failed.");
            return false;
        }
        BufferedWriter bw = null;
        try {
            bw = new BufferedWriter(new FileWriter(file, append));
            bw.write(content);
            return true;
        } catch (IOException e) {
            Log.e("FileIOUtils", "writeFileFromString: IOException " + e.getMessage());
            return false;
        } finally {
            try {
                if (bw != null) {
                    bw.close();
                }
            } catch (IOException e) {
                Log.e("FileIOUtils", "writeFileFromString: IOException " + e.getMessage());
            }
        }
    }

    private static boolean createOrExistsFile(final File file) {
        if (file == null) return false;
        if (file.exists()) return file.isFile();
        if (!createOrExistsDir(file.getParentFile())) return false;
        try {
            return file.createNewFile();
        } catch (IOException e) {
            Log.e("FileIOUtils", "createOrExistsFile: IOException " + e.getMessage());
            return false;
        }
    }

    private static boolean createOrExistsDir(final File file) {
        return file != null && (file.exists() ? file.isDirectory() : file.mkdirs());
    }

    private static byte[] readFile2BytesByStream(Context context, final File file) {
        if (!FileUtils.isFileExists(context, file)) return null;
        try {
            ByteArrayOutputStream os = null;
            InputStream is = new BufferedInputStream(new FileInputStream(file), sBufferSize);
            try {
                os = new ByteArrayOutputStream();
                byte[] b = new byte[sBufferSize];
                int len;
                while ((len = is.read(b, 0, sBufferSize)) != -1) {
                    os.write(b, 0, len);
                }
                return os.toByteArray();
            } catch (IOException e) {
                Log.e("FileIOUtils", "readFile2BytesByStream: IOException " + e.getMessage());
                return null;
            } finally {
                try {
                    is.close();
                } catch (IOException e) {
                    Log.e("FileIOUtils", "readFile2BytesByStream: IOException " + e.getMessage());
                }
                try {
                    if (os != null) {
                        os.close();
                    }
                } catch (IOException e) {
                    Log.e("FileIOUtils", "readFile2BytesByStream: IOException " + e.getMessage());
                }
            }
        } catch (FileNotFoundException e) {
            Log.e("FileIOUtils", "readFile2BytesByStream: FileNotFoundException " + e.getMessage());
            return null;
        }
    }


    public static boolean writeFileFromBytesByStream(final String filePath, final byte[] bytes) {
        return writeFileFromBytesByStream(FileUtils.getFileByPath(filePath), bytes, false);
    }

    private static boolean writeFileFromBytesByStream(final File file,
                                                      final byte[] bytes,
                                                      final boolean append) {
        if (bytes == null) return false;
        return writeFileFromIS(file, new ByteArrayInputStream(bytes), append);
    }

    private static boolean writeFileFromIS(final File file,
                                           final InputStream is,
                                           final boolean append) {
        if (is == null || !createOrExistsFile(file)) {
            Log.e("FileIOUtils", "create file <" + file + "> failed.");
            return false;
        }
        OutputStream os = null;
        try {
            os = new BufferedOutputStream(new FileOutputStream(file, append), sBufferSize);
            byte[] data = new byte[sBufferSize];
            for (int len; (len = is.read(data)) != -1; ) {
                os.write(data, 0, len);
            }
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
