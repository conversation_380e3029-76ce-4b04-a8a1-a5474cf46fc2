<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_music"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@drawable/icon_music_bg">

    <com.yk.secondary.screen.settings.view.MusicMarqueeTextView
        android:id="@+id/tv_music_name"
        android:layout_width="120px"
        android:layout_height="wrap_content"
        android:layout_marginTop="32px"
        android:singleLine="true"
        android:textColor="@android:color/white"
        android:textSize="20px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:scroll_first_delay="0"
        app:scroll_interval="15000"
        app:scroll_mode="mode_forever" />

    <ImageButton
        android:id="@+id/ib_play_pre"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10px"
        android:background="@null"
        android:src="@drawable/icon_music_pre"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/ib_play_pause"
        android:layout_width="48px"
        android:layout_height="48px"
        android:background="@null"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_music_play"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/ib_play_next"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:src="@drawable/icon_music_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="10px" />
</androidx.constraintlayout.widget.ConstraintLayout>