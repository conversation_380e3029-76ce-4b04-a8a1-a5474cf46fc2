<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:descendantFocusability="blocksDescendants">

    <pl.droidsonroids.gif.GifImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_watch2"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:id="@+id/ll_date_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="22px"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal">

            <ImageView
                android:id="@+id/iv_time_h_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false" />

            <ImageView
                android:id="@+id/iv_time_h_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/icon_watch_style2_hour_min_tag" />

            <ImageView
                android:id="@+id/iv_time_m_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false" />

            <ImageView
                android:id="@+id/iv_time_m_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9px"
            android:layout_gravity="center_horizontal">

            <ImageView
                android:id="@+id/iv_week"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <ImageView
                android:id="@+id/iv_month_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5px"
                android:focusable="false"
                android:focusableInTouchMode="false" />

            <ImageView
                android:id="@+id/iv_month_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_watch_style5_date_tag" />

            <ImageView
                android:id="@+id/iv_day_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false" />

            <ImageView
                android:id="@+id/iv_day_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false" />
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>