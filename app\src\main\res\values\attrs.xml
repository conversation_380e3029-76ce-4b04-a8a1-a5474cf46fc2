<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="secondary_settings_item">
        <attr name="title_text" format="string" />
        <attr name="sub_title_text" format="string" />
        <attr name="switch_visibility" format="boolean" />
        <attr name="arrow_visibility" format="boolean" />
    </declare-styleable>

    <declare-styleable name="Battery">
        <attr name="batteryOrientation">
            <enum name="horizontal" value="0" />
            <enum name="vertical" value="1" />
        </attr>
        <attr name="batteryColor" format="color" />
        <attr name="batteryPower" format="integer" />
    </declare-styleable>

    <declare-styleable name="Slide_view">
        <attr name="left_text" format="string" />
        <attr name="right_text" format="string" />
        <attr name="center_img" format="reference" />
        <attr name="view_bg" format="reference" />
    </declare-styleable>

    <declare-styleable name="MarqueeTextView">
        <attr name="scroll_interval" format="integer" />
        <attr name="scroll_first_delay" format="integer" />
        <attr name="scroll_mode">
            <enum name="mode_forever" value="100" />
            <enum name="mode_once" value="101" />
        </attr>
    </declare-styleable>
</resources>