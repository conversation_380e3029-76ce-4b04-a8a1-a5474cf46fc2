package com.yk.secondary.screen.settings.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.NotificationViewManager;
import com.yk.secondary.screen.settings.utils.Constant;

public class NotificationListView extends ListView {

    public NotificationListView(@NonNull Context context) {
        super(context);
        initDoubleTapListener(context);
    }

    public NotificationListView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initDoubleTapListener(context);
    }

    public NotificationListView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initDoubleTapListener(context);
    }

    private GestureDetector mGestureDetector;

    private float mScrollTotalY = 0;

    public void setScrollTotalY(float mScrollY) {
        mScrollTotalY = mScrollY;
        setTranslationY(mScrollTotalY);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mGestureDetector != null) {
            mGestureDetector.onTouchEvent(event);
        }
        return true;
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(@Nullable MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
                mScrollTotalY -= distanceY;
                if (mScrollTotalY >= 0) {
                    mScrollTotalY = 0;
                }
                if (isScrollBottom() || isScrollTop()) {
                    return true;
                }
                setTranslationY(mScrollTotalY);
                return true;
            }
        });
    }

    /**
     * 是否滑动到底部
     */
    public boolean isScrollBottom() {
        int height = (int) (App.getInstance().getContext().getResources().getDimension(R.dimen.noti_item_height) * (NotificationViewManager.getInstance().getStatusBarNotificationList().size() + 1));
        if (mScrollTotalY <= Constant.SCREEN_HEIGHT - height) {
            mScrollTotalY = Constant.SCREEN_HEIGHT - height;
        }
        return mScrollTotalY - Constant.SCREEN_HEIGHT <= -(height);
    }

    public boolean isScrollTop() {
        return mScrollTotalY >= 0;
    }
}
