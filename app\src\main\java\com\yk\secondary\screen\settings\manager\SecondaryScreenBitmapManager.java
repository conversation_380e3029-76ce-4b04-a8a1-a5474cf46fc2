package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.manager.RemindersViewManager.TYPE_ALARM;
import static com.yk.secondary.screen.settings.manager.RemindersViewManager.TYPE_PHONE;
import static com.yk.secondary.screen.settings.utils.Constant.PATH_SPI_LCM_FRAME_DATA;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.ImageUtils.convertBitmapToRGB565;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Environment;
import android.os.PowerManager;
import android.service.notification.StatusBarNotification;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.FileIOUtils;
import com.yk.secondary.screen.settings.utils.ImageUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;
import com.yk.secondary.screen.settings.utils.Utils;
import com.yk.secondary.screen.settings.view.ClockListRelativeLayout;
import com.yk.secondary.screen.settings.view.MainHorizontalScrollView;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

public class SecondaryScreenBitmapManager {

    private final AtomicBoolean isWriteBitmapDataToLcd = new AtomicBoolean(false);

    public boolean getIsWriteBitmapDataToLcd() {
        return isWriteBitmapDataToLcd.get();
    }

    //灭屏前写一张黑色背景图片
    private final AtomicBoolean mBlackBgSuccess = new AtomicBoolean(false);

    public boolean getBlackBgSuccess() {
        return mBlackBgSuccess.get();
    }

    private final LayoutInflater mLayoutInflater;

    private MainHorizontalScrollView mMainHorizontalScrollView;

    private ClockListRelativeLayout mClockListRelativeLayout;

    private FrameLayout mRootView;

    /**
     * 是否刷新副屏图片
     */
    private boolean isRefreshSecondaryScreenImage = true;

    public void setIsRefreshSecondaryScreenImage(boolean refreshSecondaryScreenImage) {
        isRefreshSecondaryScreenImage = refreshSecondaryScreenImage;
    }

    private SecondaryScreenBitmapManager() {
        mLayoutInflater = LayoutInflater.from(App.getInstance().getContext());
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final SecondaryScreenBitmapManager instance = new SecondaryScreenBitmapManager();
    }

    public static SecondaryScreenBitmapManager getInstance() {
        return SingletonHolder.instance;
    }

    public LayoutInflater getLayoutInflater() {
        return mLayoutInflater;
    }

    private PowerManager.WakeLock mWakeLock;

    private TextView mTvTip;

    private boolean mIsInitialized = false;

    /**
     * 初始化
     */
    @SuppressLint({"InvalidWakeLockTag", "WakelockTimeout", "InflateParams"})
    public void init() {
        Log.i(TAG_LOG, "SecondaryScreenBitmapManager init: mIsInitialized = "+mIsInitialized);
        if (mIsInitialized) {
            return;
        }
        acquireWakeLock();
        mRootView = (FrameLayout) mLayoutInflater.inflate(R.layout.view_bitmap_main, null);
        mTvTip = mRootView.findViewById(R.id.tv_tip);
        mMainHorizontalScrollView = mRootView.findViewById(R.id.yk_hsv);
        mMainHorizontalScrollView.setChildGroup(mRootView.findViewById(R.id.container_main));
        mMainHorizontalScrollView.init();
        mClockListRelativeLayout = mRootView.findViewById(R.id.clock_list_layout);
        ClockListViewManager.getInstance().setRootView(mClockListRelativeLayout);
        FactoryTestManager.getInstance().setRootView(mRootView.findViewById(R.id.fl_factory_test_view));
        RemindersViewManager.getInstance().setRootView(mRootView.findViewById(R.id.fl_reminders_view));
        CameraManager.getInstance().setCameraRootView(mRootView.findViewById(R.id.fl_camera));
        PhotoManager.getInstance().setPhotoRootView(mRootView.findViewById(R.id.fl_photo));
        BatteryViewManager.getInstance().setMainHorizontalScrollView(mMainHorizontalScrollView);
        BatteryViewManager.getInstance().setRootView(mRootView.findViewById(R.id.fl_battery));
        NotificationViewManager.getInstance().setMainHorizontalScrollView(mMainHorizontalScrollView);
        NotificationViewManager.getInstance().setRootView(mRootView.findViewById(R.id.fl_message));

        //开启触摸事件监听
        App.getInstance().startSecondaryScreenEventListener((action, x, y, downTime, eventTime) -> {
            if (!Utils.isDeviceProvisioned(App.getInstance().getContext())) {
                if (mTvTip != null && mTvTip.getVisibility() == View.GONE) {
                    mTvTip.setVisibility(View.VISIBLE);
                    ThreadUtils.runOnUiThreadDelayed(() -> mTvTip.setVisibility(View.GONE), 2000);
                }
                return;
            }

            MotionEvent motionEvent = MotionEvent.obtain(downTime, eventTime, action, x, y, 0);
            if (RemindersViewManager.getInstance().isShow()) {
                if (RemindersViewManager.getInstance().getType() == TYPE_ALARM) {
                    if (RemindersViewManager.getInstance().getSlideSwitchView() != null) {
                        RemindersViewManager.getInstance().getSlideSwitchView().onTouchEvent(motionEvent);
                        return;
                    }
                } else if (RemindersViewManager.getInstance().getType() == TYPE_PHONE) {
                    if (TelephonyManager.EXTRA_STATE_OFFHOOK.equals(RemindersViewManager.getInstance().getPhotoState())) {
                        if (RemindersViewManager.getInstance().getRootView() != null) {
                            RemindersViewManager.getInstance().getRootView().onTouchEvent(motionEvent);
                            return;
                        }
                    } else if (TelephonyManager.EXTRA_STATE_RINGING.equals(RemindersViewManager.getInstance().getPhotoState())) {
                        if (RemindersViewManager.getInstance().getSlideSwitchView() != null) {
                            RemindersViewManager.getInstance().getSlideSwitchView().onTouchEvent(motionEvent);
                            return;
                        }
                    }
                }
            }

            if (FactoryTestManager.getInstance().isFactoryTest() && FactoryTestManager.getInstance().getFactoryTestLineTestView() != null) {
                FactoryTestManager.getInstance().getFactoryTestLineTestView().onTouchEvent(motionEvent);
                return;
            }

            if (CameraManager.getInstance().isShow() && CameraManager.getInstance().getCameraRootView() != null) {
                CameraManager.getInstance().getCameraRootView().onTouchEvent(motionEvent);
                return;
            }

            if (PhotoManager.getInstance().isShow() && PhotoManager.getInstance().getPhotoRootView() != null) {
                PhotoManager.getInstance().getPhotoRootView().onTouchEvent(motionEvent);
                return;
            }

            if (BatteryViewManager.getInstance().isShow() && BatteryViewManager.getInstance().getRootView() != null) {
                BatteryViewManager.getInstance().getRootView().onTouchEvent(motionEvent);
                return;
            }

            if (NotificationViewManager.getInstance().isShow() && NotificationViewManager.getInstance().getRootView() != null) {
                NotificationViewManager.getInstance().getRootView().onTouchEvent(motionEvent);
                return;
            }

            if (ClockListViewManager.getInstance().isShowClockListView()) {
                if (mClockListRelativeLayout != null) {
                    mClockListRelativeLayout.onTouchEvent(motionEvent);
                    return;
                }
            }

            if (mMainHorizontalScrollView != null) {
                mMainHorizontalScrollView.onTouchEvent(motionEvent);
            }
        });

        if (RemindersViewManager.getInstance().isShow()) {
            StatusBarNotification sbn = RemindersViewManager.getInstance().getPhoneStatusBarNotification();
            if (sbn != null) {
                RemindersViewManager.getInstance().showRemindersView(TYPE_PHONE, sbn);
            }
        } else if (RemindersViewManager.getInstance().getType() == TYPE_PHONE) {//说明有提醒消息需要显示
            RemindersViewManager.getInstance().showRemindersView(TYPE_PHONE, RemindersViewManager.getInstance().getPhoneStatusBarNotification());
        } else if (RemindersViewManager.getInstance().getType() == TYPE_ALARM) {//说明有提醒消息需要显示
            RemindersViewManager.getInstance().showRemindersView(TYPE_ALARM, RemindersViewManager.getInstance().getAlarmStatusBarNotification());
        }

        //刷图到副屏显示
        setIsRefreshSecondaryScreenImage(true);
        refreshSecondaryScreenImage();
        mIsInitialized = true;
    }

    public void sendGoHomeTimeMsg() {
        if (mMainHorizontalScrollView != null) {
            mMainHorizontalScrollView.sendGoHomeTimeMsg();
        }
    }

    public void removeGoHomeTimeMsg() {
        if (mMainHorizontalScrollView != null) {
            mMainHorizontalScrollView.removeGoHomeTimeMsg();
        }
    }

    public void switchWatchStyle() {
        if (mMainHorizontalScrollView != null && mMainHorizontalScrollView.getCurViewPage() == MainHorizontalScrollView.VIEW_WATCH) {
            WatchViewManager.getInstance().switchWatchStyle(false);
        }
    }

    public void showWatchView() {
        if (mMainHorizontalScrollView != null) {
            mMainHorizontalScrollView.showWatchView();
        }
    }

    /**
     * 开启单独的线程，一直往底层写bitmap，刷新副屏
     */
    public void refreshSecondaryScreenImage() {
        isWriteBitmapDataToLcd.set(true);
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                mBlackBgSuccess.set(false);
                while (isRefreshSecondaryScreenImage) {
                    Bitmap bitmap = ImageUtils.createBitmapByView(mRootView);
                    /*File downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
                    String fileName = "IMG_" + new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date()) + ".png";
                    File imageFile = new File(downloadDir, fileName);
                    String filePath = imageFile.getAbsolutePath();
                    ImageUtils.save(bitmap, filePath, Bitmap.CompressFormat.JPEG);*/
                    if (bitmap != null) {
                        FileIOUtils.writeFileFromBytesByStream(PATH_SPI_LCM_FRAME_DATA, convertBitmapToRGB565(bitmap));
                        if (!bitmap.isRecycled()) {
                            bitmap.recycle();
                        }
                    }
                }

                //灭屏前写一张全黑的图片
                @SuppressLint("InflateParams") Bitmap bitmap = ImageUtils.createBitmapByView(LayoutInflater.from(App.getInstance().getContext()).inflate(R.layout.view_black_bg, null));
                FileIOUtils.writeFileFromBytesByStream(PATH_SPI_LCM_FRAME_DATA, ImageUtils.convertBitmapToRGB565(bitmap));
                Log.i(TAG_LOG, "refreshSecondaryScreenImage doInBackground: 写入全黑的图片 ");
                if (!bitmap.isRecycled()) {
                    bitmap.recycle();
                }
                mBlackBgSuccess.set(true);
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                Log.i(TAG_LOG, "refreshSecondaryScreenImage onSuccess: ");
                isWriteBitmapDataToLcd.set(false);
            }

            @Override
            public void onFail(Throwable t) {
                isWriteBitmapDataToLcd.set(false);
                Log.e(TAG_LOG, "refreshSecondaryScreenImage onFail: " + t.toString());
                refreshSecondaryScreenImage();
            }
        });
    }

    @SuppressLint("InvalidWakeLockTag")
    private void acquireWakeLock() {
        Log.i(TAG_LOG, "acquireWakeLock: ");
        PowerManager powerManager = (PowerManager) App.getInstance().getContext().getSystemService(Context.POWER_SERVICE);
        if (powerManager != null) {
            mWakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "secondary_screen");
            // 判断WakeLock是否已经被获取
            if (!mWakeLock.isHeld()) {
                // 获取WakeLock
                mWakeLock.acquire();
            }
        }
    }

    public void releaseWakeLock() {
        Log.i(TAG_LOG, "releaseWakeLock: ");
        if (mWakeLock != null && mWakeLock.isHeld()) {
            // 释放WakeLock
            mWakeLock.release();
            mWakeLock = null;
        }
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "SecondaryScreenBitmapManager onDestroy: ");
        setIsRefreshSecondaryScreenImage(false);
        App.getInstance().stopSecondaryScreenEventListener();
        PhotoManager.getInstance().onDestroy(false);
        CameraManager.getInstance().onDestroy(false);
        RemindersViewManager.getInstance().dismissRemindersView(true);
        BatteryViewManager.getInstance().onDestroy();
        NotificationViewManager.getInstance().onDestroy();

        ThreadUtils.getMainHandler().removeCallbacksAndMessages(null);

        if (mClockListRelativeLayout != null) {
            mClockListRelativeLayout.onDestroy();
            mClockListRelativeLayout = null;
        }

        if (mMainHorizontalScrollView != null) {
            mMainHorizontalScrollView.onDestroy();
            mMainHorizontalScrollView = null;
        }

        if (mRootView != null) {
            mRootView.removeAllViews();
            mRootView = null;
        }
        mIsInitialized = false;
    }
}
