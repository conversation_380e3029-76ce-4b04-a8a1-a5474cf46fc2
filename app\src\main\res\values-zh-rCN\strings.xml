<resources>
    <string name="unknown">未知</string>
    <string name="no_sms">无通知</string>
    <string name="sms">通知</string>
    <string name="new_message">新短信</string>
    <string name="alarm_remind">闹钟提醒</string>
    <string name="delay">延后</string>
    <string name="stop">停止</string>
    <string name="decline">拒接</string>
    <string name="answer">接听</string>
    <string name="Incoming_call">来电</string>
    <string name="camera_tip">主屏相机正在运行中</string>
    <string name="go_home_time">返回首页时间</string>
    <string name="main_tip">请先完成开机向导操作</string>
    <string name="MSensor_East">东</string>
    <string name="MSensor_North">北</string>
    <string name="MSensor_South">南</string>
    <string name="MSensor_West">西</string>
    <string name="MSensor_north_east">东北</string>
    <string name="MSensor_north_west">西北</string>
    <string name="MSensor_south_east">东南</string>
    <string name="MSensor_south_west">西南</string>
    <string name="always_led_flash">总是闪烁</string>
    <string name="always_led_flash_time">闪烁时间</string>
    <string name="animal_style_title">萌宠</string>
    <string name="app_name">副屏</string>
    <string name="backlight_level">背光等级</string>
    <string name="backlight_level_1">等级1</string>
    <string name="backlight_level_2">等级2</string>
    <string name="backlight_level_3">等级3</string>
    <string name="backlight_level_4">等级4</string>
    <string name="backlight_level_5">等级5</string>
    <string name="backlight_level_6">等级6</string>
    <string name="backlight_level_7">等级7</string>
    <string name="backlight_level_8">等级8</string>
    <string name="battery_charging_led_title">充电时</string>
    <string name="battery_current_title">电流显示</string>
    <string name="battery_level_title">电量显示</string>
    <string name="battery_voltage_title">电压显示</string>
    <string name="call_notification_answer_action">接听</string>
    <string name="call_notification_answer_video_action">视频通话</string>
    <string name="call_notification_decline_action">拒接</string>
    <string name="call_notification_hang_up_action">挂断</string>
    <string name="call_notification_incoming_text">来电</string>
    <string name="call_notification_ongoing_text">正在通话</string>
    <string name="call_notification_screening_text">正在过滤来电</string>
    <string name="camera_occupied">相机被占用!</string>
    <string name="clock_style_title">钟表</string>
    <string name="compass_app_name">指南针</string>
    <string name="copy">复制</string>
    <string name="delete">删除</string>
    <string name="display_settings">显示设置</string>
    <string name="double_touch_summary">灭屏状态下双击副屏唤醒</string>
    <string name="double_touch_title">双击亮屏</string>
    <string name="edit">编辑</string>
    <string name="expand_button_title">高级</string>
    <string name="eye_protect_level_1">等级1</string>
    <string name="eye_protect_level_2">等级2</string>
    <string name="eye_protect_level_3">等级3</string>
    <string name="eye_protect_level_4">等级4</string>
    <string name="eye_protect_level_no">关闭</string>
    <string name="eye_protect_picker_title">护眼模式</string>
    <string name="incoming_led_title">来电</string>
    <string name="low_battery_led_title">低电量</string>
    <string name="miniscreen_help_clock_camera">相机界面</string>
    <string name="miniscreen_help_clock_noti">通知界面</string>
    <string name="miniscreen_help_clock_select">表盘选择</string>
    <string name="miniscreen_help_clock_status">状态界面</string>
    <string name="miniscreen_help_setting">使用说明</string>
    <string name="miss_call_led_color_title">未接来电</string>
    <string name="not_set">未设置</string>
    <string name="notification_no_items">无通知</string>
    <string name="other_notifi_color_title">其他通知</string>
    <string name="pick_new_photo">选择新照片</string>
    <string name="pick_photo">选择照片</string>
    <string name="race_text_settings_base_title">基本设置</string>
    <string name="race_text_settings_mode_title">循环模式</string>
    <string name="race_text_settings_speed_title">滚动速度</string>
    <string name="race_text_settings_text_battery_title">显示电量</string>
    <string name="race_text_settings_text_color_title">字体颜色</string>
    <string name="race_text_settings_text_edit_title">编辑签名</string>
    <string name="race_text_settings_text_size_title">字体大小</string>
    <string name="race_text_settings_text_time_title">显示时间</string>
    <string name="race_text_settings_text_title">签名设置</string>
    <string name="remove_photo">删除照片</string>
    <string name="replace_photo">更换照片</string>
    <string name="screen_backhome_never">不返回</string>
    <string name="screen_idle_led_title">待机显示</string>
    <string name="screen_timeout">屏幕超时</string>
    <string name="screen_timeout_10_min">10 分钟</string>
    <string name="screen_timeout_15_sec">15 秒</string>
    <string name="screen_timeout_1_min">1 分钟</string>
    <string name="screen_timeout_2_min">2 分钟</string>
    <string name="screen_timeout_30_min">30 分钟</string>
    <string name="screen_timeout_30_sec">30 秒</string>
    <string name="screen_timeout_5_min">5 分钟</string>
    <string name="screen_timeout_5_sec">5 秒</string>
    <string name="screen_timeout_never">永不灭屏</string>
    <string name="screenon_flip_summary">将手机从正面翻转到背面点亮副屏</string>
    <string name="screenon_flip_title">翻转亮屏</string>
    <string name="screenon_noti_summary">息屏状态下，收到通知自动亮屏</string>
    <string name="screenon_noti_title">通知亮屏</string>
    <string name="screenon_setting">亮屏设置</string>
    <string name="screenon_with_mainscreen_summary">副屏随主屏亮</string>
    <string name="screenon_with_mainscreen_title">随主屏</string>
    <string name="search_menu_title">搜索</string>
    <string name="smart_data_line_info">此功能需配合Unihertz智能数据线使用 ！</string>
    <string name="smart_line_settings_search_key_words">智能, 数据, 线, 数据线</string>
    <string name="smart_line_title">智能数据线</string>
    <string name="smart_page_scroller_settings_search_key_words">翻, 翻屏, 助手</string>
    <string name="start_use">启用</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="subscreen_aod_summary">息屏显示开启后将不再响应触摸事件，表盘随机切换，且功耗会有增加，请酌情使用。</string>
    <string name="subscreen_aod_title">息屏显示</string>
    <string name="subscreen_back_camera_photo_size">后摄像头照片大小</string>
    <string name="subscreen_call_setting">通话设置</string>
    <string name="subscreen_call_show_summary">当来电通话时副屏会自动亮起并显示通话信息。</string>
    <string name="subscreen_call_show_title">通话提示</string>
    <string name="subscreen_camera_setting">相机设置</string>
    <string name="subscreen_clock_add">自定义表盘</string>
    <string name="subscreen_clock_display">表盘背景</string>
    <string name="subscreen_clock_nobg">请添加表盘背景！</string>
    <string name="subscreen_clock_plate">表盘</string>
    <string name="subscreen_clock_plate_setting">表盘设置</string>
    <string name="subscreen_clock_select">表盘选择</string>
    <string name="subscreen_compass_select">指南针选择</string>
    <string name="subscreen_front_camera_photo_size">前摄像头照片大小</string>
    <string name="subscreen_gohome_time">返回首页时间</string>
    <string name="subscreen_hands_select">指针选择</string>
    <string name="subscreen_marks_select">刻度选择</string>
    <string name="subscreen_music_setting">音乐设置</string>
    <string name="subscreen_noti_setting">通知设置</string>
    <string name="subscreen_noti_tip_summary">表盘上显示小红点，表示有通知</string>
    <string name="subscreen_noti_tip_titile">通知提示</string>
    <string name="subscreen_open_music_app_summary">如果没有音乐应用开启，请解锁主屏，点击副屏音乐开始按钮时打开音乐应用。</string>
    <string name="subscreen_open_music_app_titile">打开音乐应用</string>
    <string name="subscreen_open_race_text_settings_summary">个性签名开启后，副屏亮屏会一直显示个性签名，直到操作副屏.</string>
    <string name="subscreen_open_race_text_settings_titile">打开个性签名</string>
    <string name="subscreen_pick_music_app">选择音乐应用</string>
    <string name="subscreen_set_race_text_settings_titile">设置个性签名</string>
    <string name="subscreen_setting">副屏设置</string>
    <string name="subscreen_setting_summary">显示设置、亮屏设置</string>
    <string name="subscreen_show_noti_apps_summary">选择可将通知显示在副屏的应用</string>
    <string name="subscreen_show_noti_apps_title">应用选择</string>
    <string name="subscreen_take_photo_voice_summary">拍照时是否打开提示音</string>
    <string name="subscreen_take_photo_voice_titile">拍照声音</string>
    <string name="take_photo">拍照</string>
    <string name="secondary_screen_dashboard_summary">开关、功能选择、功能设置</string>
</resources>