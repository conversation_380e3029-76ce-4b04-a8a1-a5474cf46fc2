package com.yk.secondary.screen.settings.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.yk.secondary.screen.settings.listener.TimeChangeCallback;

public class TimeChangeReceiver extends BroadcastReceiver {

    public TimeChangeCallback mTimeChangeCallback;

    public void setTimeChangeCallback(TimeChangeCallback timeChangeCallback) {
        this.mTimeChangeCallback = timeChangeCallback;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }

        String action = intent.getAction();
        if (TextUtils.equals(Intent.ACTION_TIME_TICK, action)) {
            if (mTimeChangeCallback != null) {
                mTimeChangeCallback.onTimeChanged();
            }
        } else {
            if (mTimeChangeCallback != null) {
                mTimeChangeCallback.onDateChanged();
            }
        }
    }
}
