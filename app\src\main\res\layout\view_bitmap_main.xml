<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height">

    <com.yk.secondary.screen.settings.view.MainHorizontalScrollView
        android:id="@+id/yk_hsv"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:layout_gravity="center"
        android:fadingEdge="none"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/container_main"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/screen_height"
            android:orientation="horizontal" />

    </com.yk.secondary.screen.settings.view.MainHorizontalScrollView>

    <include layout="@layout/clock_list_view" />

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="20px"
        android:background="@drawable/shape_news_background"
        android:fontFamily="@font/pingfang_regular"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="4px"
        android:paddingEnd="4px"
        android:text="@string/main_tip"
        android:textColor="#333333"
        android:textSize="20px"
        android:visibility="gone" />

    <com.yk.secondary.screen.settings.view.BatteryFrameLayout
        android:id="@+id/fl_battery"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:layout_gravity="bottom" />

    <com.yk.secondary.screen.settings.view.NotificationFrameLayout
        android:id="@+id/fl_message"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:layout_gravity="top" />

    <com.yk.secondary.screen.settings.view.CameraFrameLayout
        android:id="@+id/fl_camera"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:background="@android:color/black"
        android:visibility="gone" />

    <com.yk.secondary.screen.settings.view.PhotoFrameLayout
        android:id="@+id/fl_photo"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/fl_factory_test_view"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:visibility="gone" />

    <com.yk.secondary.screen.settings.view.RemindersFrameLayout
        android:id="@+id/fl_reminders_view"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:visibility="gone" />

</FrameLayout>


