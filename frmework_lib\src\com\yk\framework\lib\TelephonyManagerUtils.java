package com.yk.framework.lib;

import android.telecom.TelecomManager;
import android.content.Context;

public class TelephonyManagerUtils {
    public static void endCall(Context context) {
        TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
        if (telecomManager != null) {
            telecomManager.endCall();
        }
    }

    public static void answerRingingCall(Context context) {
        TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
        if (telecomManager != null && telecomManager.isRinging()) {
            telecomManager.acceptRingingCall();
        }
    }
}
