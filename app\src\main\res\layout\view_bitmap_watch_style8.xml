<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@drawable/icon_watch_style8_bg"
    android:descendantFocusability="blocksDescendants">

    <ImageView
        android:id="@+id/iv_week"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="44px" />

    <ImageView
        android:id="@+id/iv_am"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="44px"
        android:layout_marginTop="147px" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="220px"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_month_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_month_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style8_date_tag" />

        <ImageView
            android:id="@+id/iv_day_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_day_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="46px"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginTop="147px"
        android:layout_marginEnd="38px"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_battery_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_battery_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:id="@+id/iv_battery_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:src="@drawable/icon_watch_style8_battery_tag" />
    </LinearLayout>

    <com.yk.secondary.screen.settings.view.WatchStyle8ClockView
        android:id="@+id/ac"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_width"
        android:layout_gravity="center"
        tools:ignore="MissingConstraints" />

</FrameLayout>