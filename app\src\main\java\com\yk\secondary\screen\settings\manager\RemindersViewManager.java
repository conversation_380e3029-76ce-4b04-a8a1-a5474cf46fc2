package com.yk.secondary.screen.settings.manager;

import static android.view.View.VISIBLE;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE_IN;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_OPEN_IN;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.service.notification.StatusBarNotification;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.yk.framework.lib.TelephonyManagerUtils;
import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.listener.OnPhoneTimeListener;
import com.yk.secondary.screen.settings.listener.TimeChangeCallback;
import com.yk.secondary.screen.settings.receiver.TimeChangeReceiver;
import com.yk.secondary.screen.settings.utils.Constant;
import com.yk.secondary.screen.settings.utils.PhoneCountDownTimer;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.view.RemindersFrameLayout;
import com.yk.secondary.screen.settings.view.SlideSwitchView;

import java.util.concurrent.atomic.AtomicBoolean;

public class RemindersViewManager implements SlideSwitchView.SlideSwitchViewListener, OnPhoneTimeListener {

    private RemindersViewManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final RemindersViewManager instance = new RemindersViewManager();
    }

    public static RemindersViewManager getInstance() {
        return RemindersViewManager.SingletonHolder.instance;
    }

    public static final int TYPE_UNKNOWN = -1;
    public static final int TYPE_PHONE = 1;
    public static final int TYPE_ALARM = 2;

    private int mType = TYPE_UNKNOWN;

    public int getType() {
        return mType;
    }

    //消息界面
    private Handler mHandler;

    //闹钟界面
    private TextView mTvCurrentTime;
    private TimeChangeReceiver mTimeChangeReceiver;

    private RemindersFrameLayout mRootView;

    private SlideSwitchView mSlideSwitchView;

    private TextView mTvPhone;
    private TextView mTvTime;
    private FrameLayout mPhoneFrameLayout;
    private ImageView mIvHangup;

    private StatusBarNotification mAlarmStatusBarNotification;
    private StatusBarNotification mPhoneStatusBarNotification;

    private String mPhotoState = TelephonyManager.EXTRA_STATE_IDLE;

    public void setPhoneStatusBarNotification(StatusBarNotification phoneStatusBarNotification) {
        if (phoneStatusBarNotification == null) {
            return;
        }
        this.mPhoneStatusBarNotification = phoneStatusBarNotification;
        showRemindersView(TYPE_PHONE, this.mPhoneStatusBarNotification);
    }

    public StatusBarNotification getPhoneStatusBarNotification() {
        return mPhoneStatusBarNotification;
    }

    public StatusBarNotification getAlarmStatusBarNotification() {
        return mAlarmStatusBarNotification;
    }

    public String getPhotoState() {
        return mPhotoState;
    }

    public void setPhotoState(String photoState) {
        Log.i(TAG_LOG, "RemindersViewManager setPhotoState: photoState " + photoState);
        this.mPhotoState = photoState;
        if (TextUtils.equals(TelephonyManager.EXTRA_STATE_IDLE, mPhotoState)) {
            PhoneCountDownTimer.getInstance().cancelTimer();
            dismissRemindersView(false);
            mPhoneStatusBarNotification = null;
        } else if (TextUtils.equals(TelephonyManager.EXTRA_STATE_OFFHOOK, mPhotoState)) {
            //通话中要熄屏
            SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
        }
    }

    public SlideSwitchView getSlideSwitchView() {
        return mSlideSwitchView;
    }

    private final AtomicBoolean mIsShow = new AtomicBoolean(false);

    public boolean isShow() {
        return mIsShow.get();
    }

    private void setIsShow(boolean show) {
        mIsShow.set(show);
        if (show) {
            SecondaryScreenBitmapManager.getInstance().removeGoHomeTimeMsg();
        } else {
            SecondaryScreenBitmapManager.getInstance().sendGoHomeTimeMsg();
        }
    }

    public RemindersFrameLayout getRootView() {
        return mRootView;
    }

    public void setRootView(RemindersFrameLayout mRootView) {
        this.mRootView = mRootView;
    }

    @Override
    public void onSlideLeft() {
        Log.i(TAG_LOG, "RemindersViewManager onSlideLeft: type = " + mType);
        if (mType == TYPE_ALARM) {
            executeAlarmSlideLeft();
        } else if (mType == TYPE_PHONE) {
            executePhoneSlideLeft();
        }
    }

    @Override
    public void onSlideRight() {
        Log.i(TAG_LOG, "RemindersViewManager onSlideRight: type = " + mType);
        if (mType == TYPE_ALARM) {
            executeAlarmSlideRight();
        } else if (mType == TYPE_PHONE) {
            executePhoneSlideRight();
        }
    }

    @SuppressLint("InflateParams")
    public void showRemindersView(int type, StatusBarNotification sbn) {
        int screenStatus = SecondaryScreenControlManager.getInstance().getScreenStatus();
        Log.i(TAG_LOG, "RemindersViewManager showRemindersView:  type=" + type +
                ", sbn=" + (sbn != null ? sbn.getKey() : "null")+", screenStatus = "+screenStatus);
        if (screenStatus == STATUS_SCREEN_CLOSE || screenStatus == STATUS_SCREEN_CLOSE_IN) {
            SecondaryScreenControlManager.getInstance().switchSecondaryScreen(true, () -> showRemindersView(type, sbn));
            return;
        } else if (screenStatus == STATUS_SCREEN_OPEN_IN) {
            mType = type;
            if (mType == TYPE_PHONE) {
                mPhoneStatusBarNotification = sbn;
            } else if (mType == TYPE_ALARM) {
                mAlarmStatusBarNotification = sbn;
            }
        }

        if (mRootView == null) {
            setIsShow(false);
            return;
        }
        Log.i(TAG_LOG, "RemindersViewManager showRemindersView: ");
        mType = type;
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
        View view = null;
        if (type == TYPE_ALARM) {
            if (sbn == null) {
                dismissRemindersView(false);
                return;
            }
            mAlarmStatusBarNotification = sbn;
            registerReceiver();
            view = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_reminders_alarm, null);
            view.setLayoutParams(layoutParams);
            mTvCurrentTime = view.findViewById(R.id.tv_current_time);
            mTvCurrentTime.setText(TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH:mm")));
            mSlideSwitchView = view.findViewById(R.id.ssv);
            mSlideSwitchView.setSlideSwitchViewListener(this);
        } else if (type == TYPE_PHONE) {
            if (sbn == null) {
                dismissRemindersView(false);
                return;
            }

            if (TelephonyManager.EXTRA_STATE_RINGING.equals(mPhotoState) || TelephonyManager.EXTRA_STATE_OFFHOOK.equals(mPhotoState)) {
                Log.i(TAG_LOG, "RemindersViewManager showRemindersView: create view");
                view = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_reminders_phone, null);
                view.setLayoutParams(layoutParams);
                mTvPhone = view.findViewById(R.id.tv_phone);
                Bundle bundle = sbn.getNotification().extras;
                CharSequence title = bundle.getCharSequence(Notification.EXTRA_TITLE);
                mTvPhone.setText(title);
                mPhoneFrameLayout = view.findViewById(R.id.fl_phone);
                mTvTime = view.findViewById(R.id.tv_time);
                mIvHangup = view.findViewById(R.id.iv_hangup);
                mRootView.setOnClickListener(mIvHangup, id -> {
                    if (id == R.id.iv_hangup) {
                        if (mPhoneStatusBarNotification == null) {
                            return;
                        }

                        Notification notification = mPhoneStatusBarNotification.getNotification();
                        if (notification == null) {
                            return;
                        }

                        Notification.Action[] actions = notification.actions;
                        if (actions == null || actions.length == 0) {
                            return;
                        }

                        Notification.Action action = actions[0];
                        if (action == null) {
                            return;
                        }

                        PendingIntent pendingIntent = action.actionIntent;
                        if (pendingIntent != null) {
                            try {
                                pendingIntent.send();
                            } catch (PendingIntent.CanceledException e) {
                                Log.i(TAG_LOG, "onSlideLeft: e = " + e.getMessage());
                            }
                        }
                    }
                });
                mSlideSwitchView = view.findViewById(R.id.ssv);
                mSlideSwitchView.setSlideSwitchViewListener(this);
            }

            updateViewByState();
            if (TextUtils.equals(TelephonyManager.EXTRA_STATE_OFFHOOK, mPhotoState)) {
                PhoneCountDownTimer.getInstance().addListener(this);
                PhoneCountDownTimer.getInstance().startTimer(sbn);
            }
        }

        if (view != null) {
            Log.i(TAG_LOG, "showRemindersView: addView ");
//            mRootView.removeAllViews();
            mRootView.addView(view);
        }

        if (mRootView.getVisibility() != VISIBLE) {
            Log.i(TAG_LOG, "showRemindersView: mRootView  setVisibility is VISIBLE ");
            mRootView.setVisibility(VISIBLE);
            setIsShow(true);
        }
    }

    public void dismissRemindersView(boolean isOffScreen) {
        Log.i(TAG_LOG, "RemindersViewManager dismissRemindersView: ");
        unregisterReceiver();
        mType = TYPE_UNKNOWN;
        mAlarmStatusBarNotification = null;
        if (!isOffScreen && isShow()) {
            setIsShow(false);
            mPhoneStatusBarNotification = null;
        }
        mTvCurrentTime = null;
        mSlideSwitchView = null;
        mTvPhone = null;
        mTvTime = null;
        mPhoneFrameLayout = null;
        mIvHangup = null;
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }

        if (mRootView != null) {
            mRootView.removeAllViews();
            mRootView.clearClickListener();
            mRootView.setVisibility(View.GONE);
        }

        if (!isOffScreen) {
            SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
        }
    }

    private void updateViewByState() {
        if (mPhoneFrameLayout != null) {
            mPhoneFrameLayout.setVisibility(TextUtils.equals(TelephonyManager.EXTRA_STATE_RINGING, mPhotoState) ? View.GONE : View.VISIBLE);
        }
        if (mSlideSwitchView != null) {
            mSlideSwitchView.setVisibility(TextUtils.equals(TelephonyManager.EXTRA_STATE_RINGING, mPhotoState) ? View.VISIBLE : View.GONE);
        }
    }

    @Override
    public void onUpdatePhoneTime(String text) {
        if (mTvTime != null) {
            mTvTime.setText(text);
        }
    }

    private void executeAlarmSlideLeft() {
        if (mAlarmStatusBarNotification == null) {
            return;
        }

        Notification notification = mAlarmStatusBarNotification.getNotification();
        if (notification == null) {
            return;
        }

        Notification.Action[] actions = notification.actions;
        if (actions == null || actions.length < 2) {
            return;
        }

        Notification.Action action = actions[0];
        if (action == null) {
            return;
        }

        PendingIntent pendingIntent = action.actionIntent;
        if (pendingIntent != null) {
            try {
                pendingIntent.send();
                dismissRemindersView(false);
            } catch (PendingIntent.CanceledException e) {
                Log.i(TAG_LOG, "RemindersViewManager onSlideLeft: e = " + e.getMessage());
            }
        }
    }

    private void executeAlarmSlideRight() {
        if (mAlarmStatusBarNotification == null) {
            return;
        }

        Notification notification = mAlarmStatusBarNotification.getNotification();
        if (notification == null) {
            return;
        }

        Notification.Action[] actions = notification.actions;
        if (actions == null || actions.length < 2) {
            return;
        }

        Notification.Action action = actions[1];
        if (action == null) {
            return;
        }

        PendingIntent pendingIntent = action.actionIntent;
        if (pendingIntent != null) {
            try {
                pendingIntent.send();
                dismissRemindersView(false);
            } catch (PendingIntent.CanceledException e) {
                Log.i(TAG_LOG, "RemindersViewManager onSlideRight: e = " + e.getMessage());
            }
        }
    }

    private void executePhoneSlideLeft() {
        if (mPhoneStatusBarNotification == null) {
            return;
        }

        Notification notification = mPhoneStatusBarNotification.getNotification();
        if (notification == null) {
            return;
        }

        Notification.Action[] actions = notification.actions;
        if (actions == null || actions.length < 2) {
            return;
        }

        Notification.Action action = actions[0];
        if (action == null) {
            return;
        }
        TelephonyManagerUtils.endCall(App.getInstance().getContext());
        dismissRemindersView(false);
    }

    private void executePhoneSlideRight() {
        if (mPhoneStatusBarNotification == null) {
            return;
        }

        Notification notification = mPhoneStatusBarNotification.getNotification();
        if (notification == null) {
            return;
        }

        Notification.Action[] actions = notification.actions;
        if (actions == null || actions.length < 2) {
            return;
        }

        Notification.Action action = actions[1];
        if (action == null) {
            return;
        }
        TelephonyManagerUtils.answerRingingCall(App.getInstance().getContext());
        if (mPhoneFrameLayout != null) {
            mPhoneFrameLayout.setVisibility(View.VISIBLE);
        }
        if (mSlideSwitchView != null) {
            mSlideSwitchView.setVisibility(View.GONE);
        }
    }

    private void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_TIME_TICK);
        filter.addAction(Intent.ACTION_DATE_CHANGED);
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED);
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        if (mTimeChangeReceiver == null) {
            mTimeChangeReceiver = new TimeChangeReceiver();
            mTimeChangeReceiver.setTimeChangeCallback(new TimeChangeCallback() {
                @Override
                public void onTimeChanged() {
                    if (mTvCurrentTime != null) {
                        mTvCurrentTime.setText(TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH:mm")));
                    }
                }

                @Override
                public void onDateChanged() {
                    if (mTvCurrentTime != null) {
                        mTvCurrentTime.setText(TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("HH:mm")));
                    }
                }
            });

        }
        App.getInstance().getContext().registerReceiver(mTimeChangeReceiver, filter, Context.RECEIVER_EXPORTED);
    }

    private void unregisterReceiver() {
        if (mTimeChangeReceiver != null) {
            App.getInstance().getContext().unregisterReceiver(mTimeChangeReceiver);
            mTimeChangeReceiver = null;
        }
    }
}
