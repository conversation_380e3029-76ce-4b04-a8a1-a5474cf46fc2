<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/screen_width"
    android:background="@android:color/black"
    android:layout_height="@dimen/screen_height">

    <com.yk.secondary.screen.settings.view.PhotoHorizontalScrollView
        android:id="@+id/photo_scroll_view"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height"
        android:layout_gravity="center"
        android:fadingEdge="none"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/container_photo"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/screen_height"
            android:orientation="horizontal" />

    </com.yk.secondary.screen.settings.view.PhotoHorizontalScrollView>

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="15px"
        android:src="@drawable/ic_back" />

    <TextView
        android:id="@+id/tv_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="15px"
        android:background="@drawable/shape_photo_count_background"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="42px"
        android:minHeight="42px"
        android:textColor="@android:color/white"
        android:textSize="12px" />


</FrameLayout>
