package com.yk.framework.lib;

import android.app.ActivityManagerNative;
import android.app.IActivityManager;
import android.os.RemoteException;


public class DynamicIslandActivityManagerUtils {

    private DynamicIslandActivityController mDynamicIslandActivityController;

    public DynamicIslandActivityManagerUtils() {
    }

    public void registerActivityController(DynamicIslandActivityControllerListener dynamicIslandActivityControllerListener) {
        try {
            IActivityManager mIActivityManager = ActivityManagerNative.getDefault();
            if (this.mDynamicIslandActivityController == null) {
                this.mDynamicIslandActivityController = new DynamicIslandActivityController(dynamicIslandActivityControllerListener);
            }

            mIActivityManager.setActivityController(this.mDynamicIslandActivityController, false);
        } catch (RemoteException var3) {
        }

    }

    public void unregisterActivityController() {
        if (this.mDynamicIslandActivityController != null) {
            this.mDynamicIslandActivityController.unregisterActivityController();
            this.mDynamicIslandActivityController = null;
        }

    }
}
