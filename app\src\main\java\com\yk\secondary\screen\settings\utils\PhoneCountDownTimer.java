package com.yk.secondary.screen.settings.utils;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.os.CountDownTimer;
import android.service.notification.StatusBarNotification;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Chronometer;
import android.widget.FrameLayout;
import android.widget.RemoteViews;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.listener.OnPhoneTimeListener;

import java.util.ArrayList;

public class PhoneCountDownTimer {

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final PhoneCountDownTimer INSTANCE = new PhoneCountDownTimer();
    }

    private PhoneCountDownTimer() {
    }

    public static PhoneCountDownTimer getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private final ArrayList<OnPhoneTimeListener> mDeskClockListenerList = new ArrayList<>();

    private CountDownTimer mCountDownTimer;

    private RemoteViews mRemoteViews;

    private Chronometer mChronometer;

    private FrameLayout mFrameLayout;

    public void addListener(OnPhoneTimeListener listener) {
        mDeskClockListenerList.add(listener);
    }

    public void startTimer(StatusBarNotification sbn) {
        Log.i(TAG_LOG, "PhoneCountDownTimer startTimer: ");
        if (mCountDownTimer == null) {
            mCountDownTimer = new CountDownTimer(Long.MAX_VALUE, 1000) {

                @Override
                public void onTick(long millisUntilFinished) {
                    getChronometer((FrameLayout) mRemoteViews.apply(App.getInstance().getContext(), mFrameLayout));
                    if (mChronometer != null) {
                        for (OnPhoneTimeListener listener : mDeskClockListenerList) {
                            listener.onUpdatePhoneTime(mChronometer.getText().toString());
                        }
                    }
                }

                @Override
                public void onFinish() {
                    Log.i(TAG_LOG, "DeskClockCountDownTimer onFinish: ");
                }
            };
        }
        FrameLayout frameLayout = new FrameLayout(App.getInstance().getContext());
        Notification.Builder builder = Notification.Builder.recoverBuilder(App.getInstance().getContext(), sbn.getNotification());
        mRemoteViews = builder.createContentView();
        LayoutInflater layoutInflater = LayoutInflater.from(App.getInstance().getContext());
        mFrameLayout = (FrameLayout) layoutInflater.inflate(mRemoteViews.getLayoutId(), frameLayout);
        getChronometer((FrameLayout) mRemoteViews.apply(App.getInstance().getContext(), mFrameLayout));
        Log.i(TAG_LOG, "PhoneCountDownTimer startTimer: mChronometer " + mChronometer);
        if (mChronometer != null) {
            for (OnPhoneTimeListener listener : mDeskClockListenerList) {
                listener.onUpdatePhoneTime(mChronometer.getText().toString());
            }
            mCountDownTimer.start();
        }
    }

    public void cancelTimer() {
        Log.i(TAG_LOG, "PhoneCountDownTimer cancelTimer: ");
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }

        mDeskClockListenerList.clear();

        mRemoteViews = null;
        mChronometer = null;
        mFrameLayout = null;
    }

    private void getChronometer(ViewGroup viewGroup) {
        if (viewGroup == null) {
            return;
        }
        int childCount = viewGroup.getChildCount();
        if (childCount > 0) {
            for (int i = 0; i < childCount; i++) {
                View view = viewGroup.getChildAt(i);
                if (view instanceof ViewGroup) {
                    getChronometer((ViewGroup) view);
                } else {
                    if (view instanceof Chronometer) {
                        mChronometer = (Chronometer) view;
                    }
                }
            }
        }
    }
}
