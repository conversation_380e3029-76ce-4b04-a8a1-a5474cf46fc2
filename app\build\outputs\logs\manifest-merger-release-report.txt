-- Merging decision tree log ---
manifest
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:2:1-127:12
INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:2:1-127:12
INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:2:1-127:12
INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:2:1-127:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\7168a5ca3163de8ac428f4c69849f2f7\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\9dd91670c9a51a4dafc2561bc2665145\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\bfebd3918c752036139dc75cfa7f9ad9\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\eb34c7585533eba98715e1f87b2e503f\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\738f676dd8bfe0cfc519e1cad4e1f9f8\transformed\glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a728632cce345c41abefc1f153cabd20\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\dca669420ce497cdc4b56e6fb9763ac8\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\621f09a9d51910886b15ca7b79ca65c4\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\929d35f0e063c733410b049a185d2b20\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\47dfe2593194508827c7e10bb7c2b1cd\transformed\camera-core-1.3.4\AndroidManifest.xml:17:1-36:12
MERGED from [pl.droidsonroids.gif:android-gif-drawable:1.2.29] C:\Users\<USER>\.gradle\caches\transforms-4\f51f10de7ace7dd72abaf640887a48e5\transformed\android-gif-drawable-1.2.29\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3342ce3e471d4d90c2347bde6a6d23c6\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d6d51e96e18ca2611090adb1f3deb41\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\97b1232c8025b9dfaf081ab67a80ba91\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d44a082a8fa76f95a657b2bc9279eb8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\325d18d42b983581c79fd0ef03703228\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\babc24b40abb92e3dbbc16d4ebdeede9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\707a5f33ec23cdc7d33ac1dcfe2aac6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d1b63e1ff3c407c574e999c454f5f1db\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ff4c9244ab3a21c62f41e93c37eb129e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f5641bf8d37a2026264287db36ac67a3\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98b5fdf8da1333a5749a522b5f013e88\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\14d20612d3d37baecf045f761299816f\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf65c708baf4f7c56b3ea7d519d7fe44\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\728178660ec3cf360bc351e2dc45bdc7\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd588b5678fa8e32a42c62ca2ae76642\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\d74b0c8b370e17cb071620729b2feea6\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b6c89b9fdb61f5ad1a09ef73e97ae7d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64c947cf532dea12768a0a53afbd7f66\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\08fc4208944ca1791db6ee92d8586dd0\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c25203d65984d4f759d81b39c5088bcb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4c994e389f34c980c61075a847e923c1\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\10d26231b86dc4de8e672a12d798f3c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\517e2d17652678595bb3141ff17804e3\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c7381e6da459d7ed7871e490d750693f\transformed\gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\b99f48ebc2bad481f198edf6cb06d967\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf1b7c8f21330929bf455eb51e3a5332\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b354c484b95a5cd435ddd22d3527bb7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e56566d744dd10ac1062b4b9dda5c9d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\72e25e066c8a166e21366369d1ceb75e\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\27012d0839d7362d9990033280b9a010\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\270e0320513eae3067e1ee14ac7b158d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e78ebc80ed965aba9b77fe5dd0189c6\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-4\7d900b9748c991a74693305b1e09249e\transformed\relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:3:5-46
	android:versionName
		INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:5:5-81
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:5:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:6:5-68
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:6:22-65
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:10:5-85
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:10:22-82
uses-permission#android.permission.GET_INTENT_SENDER_INTENT
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:11:5-83
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:11:22-80
uses-permission#android.permission.INTERNET
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:12:5-67
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:12:22-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:13:5-76
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:13:22-73
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:14:22-76
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:15:5-76
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:15:22-73
uses-permission#android.permission.READ_SMS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.RECEIVE_SMS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:17:5-70
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:17:22-67
uses-permission#com.google.android.deskclock.permission.RECEIVE_ALERT_BROADCASTS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:18:5-104
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:18:22-101
uses-permission#com.android.alarm.permission.SET_ALARM
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:19:5-78
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:19:22-75
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:20:5-75
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:20:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:21:5-77
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:21:22-74
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.READ_PHONE_STATE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:23:5-75
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:23:22-72
uses-permission#android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:24:5-100
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:24:22-97
uses-permission#android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:25:5-118
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:25:22-115
uses-permission#android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:26:5-89
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:26:22-86
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:27:5-78
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:27:22-75
uses-permission#android.permission.SEND_SMS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:28:5-67
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:28:22-64
uses-permission#android.permission.WRITE_SMS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:29:5-31:58
	android:protectionLevel
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:31:9-55
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:30:9-52
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:32:5-82
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:32:22-79
uses-permission#android.permission.RECEIVE_MMS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:33:5-70
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:33:22-67
uses-permission#android.permission.RECEIVE_WAP_PUSH
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:34:5-75
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:34:22-72
uses-permission#android.permission.ACCESS_MESSAGES_ON_ICC
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:35:5-81
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:35:22-78
uses-permission#android.permission.CAMERA
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:37:5-65
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:37:22-62
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:38:5-76
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:38:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:39:5-81
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:39:22-78
uses-permission#android.permission.WRITE_MEDIA_STORAGE
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:40:5-78
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:40:22-75
uses-feature#android.hardware.camera
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:42:5-60
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:42:19-57
application
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:44:5-125:19
INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:44:5-125:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\7168a5ca3163de8ac428f4c69849f2f7\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\7168a5ca3163de8ac428f4c69849f2f7\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\9dd91670c9a51a4dafc2561bc2665145\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\9dd91670c9a51a4dafc2561bc2665145\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\738f676dd8bfe0cfc519e1cad4e1f9f8\transformed\glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\738f676dd8bfe0cfc519e1cad4e1f9f8\transformed\glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\47dfe2593194508827c7e10bb7c2b1cd\transformed\camera-core-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\47dfe2593194508827c7e10bb7c2b1cd\transformed\camera-core-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\10d26231b86dc4de8e672a12d798f3c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\10d26231b86dc4de8e672a12d798f3c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c7381e6da459d7ed7871e490d750693f\transformed\gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c7381e6da459d7ed7871e490d750693f\transformed\gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf1b7c8f21330929bf455eb51e3a5332\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf1b7c8f21330929bf455eb51e3a5332\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:52:9-35
	android:label
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:50:9-41
	android:fullBackupContent
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:48:9-54
	android:roundIcon
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:51:9-54
	android:icon
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:49:9-43
	android:allowBackup
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:46:9-35
	android:theme
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:53:9-61
	android:dataExtractionRules
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:47:9-65
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:45:9-28
activity#com.yk.secondary.screen.settings.ui.SettingsActivity
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:55:9-82:20
	android:screenOrientation
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:60:13-49
	android:label
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:58:13-45
	android:launchMode
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:59:13-48
	android:exported
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:57:13-37
	android:theme
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:61:13-52
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:56:13-48
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.INFO
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:62:13-67:29
action#android.intent.action.MAIN
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:63:17-69
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:63:25-66
category#android.intent.category.DEFAULT
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:65:17-76
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:65:27-73
category#android.intent.category.INFO
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:66:17-73
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:66:27-70
intent-filter#action:name:com.android.settings.action.EXTRA_SETTINGS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:68:13-70:29
	android:priority
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:68:28-48
action#com.android.settings.action.EXTRA_SETTINGS
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:69:17-85
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:69:25-82
meta-data#com.android.settings.category
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:72:13-74:77
	android:value
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:74:17-74
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:73:17-61
meta-data#com.android.settings.icon
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:75:13-77:70
	android:resource
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:77:17-67
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:76:17-57
meta-data#com.android.settings.title
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:79:13-81:52
	android:value
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:81:17-49
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:80:17-58
activity#com.yk.secondary.screen.settings.ui.ClockSelectActivity
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:84:9-89:55
	android:screenOrientation
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:88:13-49
	android:label
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:87:13-59
	android:exported
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:86:13-37
	android:theme
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:89:13-52
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:85:13-51
activity#com.yk.secondary.screen.settings.ui.MusicActivity
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:91:9-96:55
	android:screenOrientation
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:95:13-49
	android:label
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:94:13-59
	android:exported
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:93:13-37
	android:theme
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:96:13-52
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:92:13-45
activity#com.yk.secondary.screen.settings.ui.NofiAppsSelectActivity
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:98:9-103:55
	android:screenOrientation
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:102:13-49
	android:label
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:101:13-67
	android:exported
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:100:13-37
	android:theme
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:103:13-52
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:99:13-54
service#com.yk.secondary.screen.settings.service.NotificationObserverService
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:105:9-115:19
	android:label
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:109:13-45
	android:excludeFromRecents
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:107:13-46
	android:exported
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:108:13-37
	android:permission
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:110:13-87
	android:stopWithTask
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:111:13-41
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:106:13-64
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:112:13-114:29
action#android.service.notification.NotificationListenerService
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:113:17-99
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:113:25-96
service#com.yk.secondary.screen.settings.factorytest.FactoryTestService
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:116:9-124:19
	android:enabled
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:118:13-35
	android:exported
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:119:13-36
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:117:13-59
intent-filter#action:name:com.yk.aidl.factory.test+category:name:android.intent.category.DEFAULT
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:120:13-123:29
action#com.yk.aidl.factory.test
ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:121:17-67
	android:name
		ADDED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml:121:25-64
uses-sdk
INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\7168a5ca3163de8ac428f4c69849f2f7\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\7168a5ca3163de8ac428f4c69849f2f7\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\9dd91670c9a51a4dafc2561bc2665145\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\9dd91670c9a51a4dafc2561bc2665145\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\bfebd3918c752036139dc75cfa7f9ad9\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\bfebd3918c752036139dc75cfa7f9ad9\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\eb34c7585533eba98715e1f87b2e503f\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\eb34c7585533eba98715e1f87b2e503f\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\738f676dd8bfe0cfc519e1cad4e1f9f8\transformed\glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\738f676dd8bfe0cfc519e1cad4e1f9f8\transformed\glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a728632cce345c41abefc1f153cabd20\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a728632cce345c41abefc1f153cabd20\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\dca669420ce497cdc4b56e6fb9763ac8\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\dca669420ce497cdc4b56e6fb9763ac8\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\621f09a9d51910886b15ca7b79ca65c4\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\621f09a9d51910886b15ca7b79ca65c4\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\929d35f0e063c733410b049a185d2b20\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\929d35f0e063c733410b049a185d2b20\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\47dfe2593194508827c7e10bb7c2b1cd\transformed\camera-core-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\47dfe2593194508827c7e10bb7c2b1cd\transformed\camera-core-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [pl.droidsonroids.gif:android-gif-drawable:1.2.29] C:\Users\<USER>\.gradle\caches\transforms-4\f51f10de7ace7dd72abaf640887a48e5\transformed\android-gif-drawable-1.2.29\AndroidManifest.xml:5:5-44
MERGED from [pl.droidsonroids.gif:android-gif-drawable:1.2.29] C:\Users\<USER>\.gradle\caches\transforms-4\f51f10de7ace7dd72abaf640887a48e5\transformed\android-gif-drawable-1.2.29\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3342ce3e471d4d90c2347bde6a6d23c6\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\3342ce3e471d4d90c2347bde6a6d23c6\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d6d51e96e18ca2611090adb1f3deb41\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d6d51e96e18ca2611090adb1f3deb41\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\97b1232c8025b9dfaf081ab67a80ba91\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\97b1232c8025b9dfaf081ab67a80ba91\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d44a082a8fa76f95a657b2bc9279eb8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d44a082a8fa76f95a657b2bc9279eb8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\325d18d42b983581c79fd0ef03703228\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\325d18d42b983581c79fd0ef03703228\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\babc24b40abb92e3dbbc16d4ebdeede9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\babc24b40abb92e3dbbc16d4ebdeede9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\707a5f33ec23cdc7d33ac1dcfe2aac6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\707a5f33ec23cdc7d33ac1dcfe2aac6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d1b63e1ff3c407c574e999c454f5f1db\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d1b63e1ff3c407c574e999c454f5f1db\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ff4c9244ab3a21c62f41e93c37eb129e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ff4c9244ab3a21c62f41e93c37eb129e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f5641bf8d37a2026264287db36ac67a3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f5641bf8d37a2026264287db36ac67a3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98b5fdf8da1333a5749a522b5f013e88\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98b5fdf8da1333a5749a522b5f013e88\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\14d20612d3d37baecf045f761299816f\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\14d20612d3d37baecf045f761299816f\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf65c708baf4f7c56b3ea7d519d7fe44\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf65c708baf4f7c56b3ea7d519d7fe44\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\728178660ec3cf360bc351e2dc45bdc7\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\728178660ec3cf360bc351e2dc45bdc7\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd588b5678fa8e32a42c62ca2ae76642\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd588b5678fa8e32a42c62ca2ae76642\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\d74b0c8b370e17cb071620729b2feea6\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\d74b0c8b370e17cb071620729b2feea6\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b6c89b9fdb61f5ad1a09ef73e97ae7d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b6c89b9fdb61f5ad1a09ef73e97ae7d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64c947cf532dea12768a0a53afbd7f66\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\64c947cf532dea12768a0a53afbd7f66\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\08fc4208944ca1791db6ee92d8586dd0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\08fc4208944ca1791db6ee92d8586dd0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c25203d65984d4f759d81b39c5088bcb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c25203d65984d4f759d81b39c5088bcb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4c994e389f34c980c61075a847e923c1\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4c994e389f34c980c61075a847e923c1\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\10d26231b86dc4de8e672a12d798f3c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\10d26231b86dc4de8e672a12d798f3c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\517e2d17652678595bb3141ff17804e3\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\517e2d17652678595bb3141ff17804e3\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c7381e6da459d7ed7871e490d750693f\transformed\gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c7381e6da459d7ed7871e490d750693f\transformed\gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\b99f48ebc2bad481f198edf6cb06d967\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\b99f48ebc2bad481f198edf6cb06d967\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf1b7c8f21330929bf455eb51e3a5332\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf1b7c8f21330929bf455eb51e3a5332\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b354c484b95a5cd435ddd22d3527bb7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b354c484b95a5cd435ddd22d3527bb7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e56566d744dd10ac1062b4b9dda5c9d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e56566d744dd10ac1062b4b9dda5c9d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\72e25e066c8a166e21366369d1ceb75e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\72e25e066c8a166e21366369d1ceb75e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\27012d0839d7362d9990033280b9a010\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\27012d0839d7362d9990033280b9a010\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\270e0320513eae3067e1ee14ac7b158d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\270e0320513eae3067e1ee14ac7b158d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e78ebc80ed965aba9b77fe5dd0189c6\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e78ebc80ed965aba9b77fe5dd0189c6\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-4\7d900b9748c991a74693305b1e09249e\transformed\relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-4\7d900b9748c991a74693305b1e09249e\transformed\relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from G:\BitmapSecondaryScreen\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\47dfe2593194508827c7e10bb7c2b1cd\transformed\camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\47dfe2593194508827c7e10bb7c2b1cd\transformed\camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\transforms-4\ac7bb04db94f16eb7b09969f983ba53d\transformed\camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\10d26231b86dc4de8e672a12d798f3c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\10d26231b86dc4de8e672a12d798f3c3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd803d3f927c525059ea0e5d1a1d72db\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fc202c826b1ff89855d87c147da63c9f\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.yk.secondary.screen.settings.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.yk.secondary.screen.settings.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\ea3f6bb26fe6709ab0cf520a628217ab\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\914373a17337534733ce5973ad1a8398\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
