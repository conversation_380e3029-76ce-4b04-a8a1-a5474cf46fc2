<resources>
    <string name="unknown">unknown</string>
    <string name="no_sms">No notification</string>
    <string name="sms">Notification</string>
    <string name="new_message">New message</string>
    <string name="alarm_remind">Alarm</string>
    <string name="delay">Snooze</string>
    <string name="stop">Stop</string>
    <string name="decline">Decline</string>
    <string name="answer">Answer</string>
    <string name="Incoming_call">Incoming call</string>
    <string name="camera_tip">The main camera is running</string>
    <string name="go_home_time">Go home time</string>
    <string name="main_tip">Complete the boot wizard first</string>
    <string name="MSensor_East">East</string>
    <string name="MSensor_North">North</string>
    <string name="MSensor_South">South</string>
    <string name="MSensor_West">West</string>
    <string name="MSensor_north_east">Eastnorth</string>
    <string name="MSensor_north_west">Westnorth</string>
    <string name="MSensor_south_east">Eastsouth</string>
    <string name="MSensor_south_west">Westsouth</string>
    <string name="always_led_flash">Always on</string>
    <string name="always_led_flash_time">Flashing time</string>
    <string name="animal_style_title">Cute pet</string>
    <string name="app_name">MiniScreen</string>
    <string name="backlight_level">Backlight level</string>
    <string name="backlight_level_1">Level 1</string>
    <string name="backlight_level_2">Level 2</string>
    <string name="backlight_level_3">Level 3</string>
    <string name="backlight_level_4">Level 4</string>
    <string name="backlight_level_5">Level 5</string>
    <string name="backlight_level_6">Level 6</string>
    <string name="backlight_level_7">Level 7</string>
    <string name="backlight_level_8">Level 8</string>
    <string name="battery_charging_led_title">Charging</string>
    <string name="battery_current_title">Charging current</string>
    <string name="battery_level_title">Battery level</string>
    <string name="battery_voltage_title">Charging voltage</string>
    <string name="call_notification_answer_action">Answer</string>
    <string name="call_notification_answer_video_action">Video</string>
    <string name="call_notification_decline_action">Decline</string>
    <string name="call_notification_hang_up_action">Hang Up</string>
    <string name="call_notification_incoming_text">Incoming call</string>
    <string name="call_notification_ongoing_text">Ongoing call</string>
    <string name="call_notification_screening_text">Screening an incoming call</string>
    <string name="camera_occupied">Camera occupied!</string>
    <string name="clock_style_title">Clock style</string>
    <string name="compass_app_name">Compass</string>
    <string name="copy">Copy</string>
    <string name="delete">Delete</string>
    <string name="display_settings">Display Settings</string>
    <string name="double_touch_summary">When the subscreen is off, double tap on it to light the subscreen.</string>
    <string name="double_touch_title">Double tap</string>
    <string name="edit">Edit</string>
    <string name="expand_button_title">Advanced</string>
    <string name="eye_protect_level_1">Level 1</string>
    <string name="eye_protect_level_2">Level 2</string>
    <string name="eye_protect_level_3">Level 3</string>
    <string name="eye_protect_level_4">Level 4</string>
    <string name="eye_protect_level_no">Close</string>
    <string name="eye_protect_picker_title">Eye protect</string>
    <string name="incoming_led_title">Incoming calls</string>
    <string name="low_battery_led_title">Battery low!</string>
    <string name="miniscreen_help_clock_camera">Camera screen</string>
    <string name="miniscreen_help_clock_noti">Notification screen</string>
    <string name="miniscreen_help_clock_select">Clock select</string>
    <string name="miniscreen_help_clock_status">Status screen</string>
    <string name="miniscreen_help_setting">Use help</string>
    <string name="miss_call_led_color_title">Calls</string>
    <string name="not_set">Not set</string>
    <string name="notification_no_items">No notification</string>
    <string name="other_notifi_color_title">Notifications</string>
    <string name="pick_new_photo">Pick new photo</string>
    <string name="pick_photo">Pick photo</string>
    <string name="race_text_settings_base_title">Basic Settings</string>
    <string name="race_text_settings_mode_title">Cycle Mode</string>
    <string name="race_text_settings_speed_title">Rolling speed</string>
    <string name="race_text_settings_text_battery_title">Display battery level</string>
    <string name="race_text_settings_text_color_title">Font color</string>
    <string name="race_text_settings_text_edit_title">Edit signature</string>
    <string name="race_text_settings_text_size_title">Font size</string>
    <string name="race_text_settings_text_time_title">Display time</string>
    <string name="race_text_settings_text_title">Signature settings</string>
    <string name="remove_photo">Remove photo</string>
    <string name="replace_photo">Replace photo</string>
    <string name="screen_backhome_never">Never</string>
    <string name="screen_idle_led_title">Standby display</string>
    <string name="screen_timeout">Screen timeout</string>
    <string name="screen_timeout_10_min">10 minute</string>
    <string name="screen_timeout_15_sec">15 seconds</string>
    <string name="screen_timeout_1_min">1 minute</string>
    <string name="screen_timeout_2_min">2 minute</string>
    <string name="screen_timeout_30_min">30 minute</string>
    <string name="screen_timeout_30_sec">30 seconds</string>
    <string name="screen_timeout_5_min">5 minute</string>
    <string name="screen_timeout_5_sec">5 seconds</string>
    <string name="screen_timeout_never">Never</string>
    <string name="screenon_flip_summary">Subscreen on when turn phone from front to back</string>
    <string name="screenon_flip_title">Flip to screen on</string>
    <string name="screenon_noti_summary">Turn on the screen when notifications are received</string>
    <string name="screenon_noti_title">Notifications turn on screen</string>
    <string name="screenon_setting">Screen on settings</string>
    <string name="screenon_with_mainscreen_summary">Screen on with main screen</string>
    <string name="screenon_with_mainscreen_title">With main screen</string>
    <string name="search_menu_title">Search</string>
    <string name="smart_data_line_info">Please use Unihertz uCable !</string>
    <string name="smart_line_settings_search_key_words">Intelligent, data, cable, USB cable</string>
    <string name="smart_line_title">uCable</string>
    <string name="smart_page_scroller_settings_search_key_words">scroll, scroller, page, assistant</string>
    <string name="start_use">Use</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="subscreen_aod_summary">After turned on, there will be no response to touch events. The dial will switch randomly and power consumption will increase, please use it as appropriate</string>
    <string name="subscreen_aod_title">Off screen display</string>
    <string name="subscreen_back_camera_photo_size">Back camera photo size</string>
    <string name="subscreen_call_setting">Call Settings</string>
    <string name="subscreen_call_show_summary">When a call is made, the secondary screen will automatically light up and display call information.</string>
    <string name="subscreen_call_show_title">Call tip</string>
    <string name="subscreen_camera_setting">Camera settings</string>
    <string name="subscreen_clock_add">Custom clock</string>
    <string name="subscreen_clock_display">Clock background</string>
    <string name="subscreen_clock_nobg">Please add clock background!</string>
    <string name="subscreen_clock_plate">Clock plate</string>
    <string name="subscreen_clock_plate_setting">Clock plate settings</string>
    <string name="subscreen_clock_select">Select clock plate</string>
    <string name="subscreen_compass_select">Select Compass Plate</string>
    <string name="subscreen_front_camera_photo_size">Front camera photo size</string>
    <string name="subscreen_gohome_time">Go home time</string>
    <string name="subscreen_hands_select">Select clock hands</string>
    <string name="subscreen_marks_select">Select clock marks</string>
    <string name="subscreen_music_setting">Music settings</string>
    <string name="subscreen_noti_setting">Notification settings</string>
    <string name="subscreen_noti_tip_summary">When notification is coming, a red point will be display on clock</string>
    <string name="subscreen_noti_tip_titile">Notification tip</string>
    <string name="subscreen_open_music_app_summary">Unlock the main screen, open selected music app when click music start button if no music app was started</string>
    <string name="subscreen_open_music_app_titile">Open music app</string>
    <string name="subscreen_open_race_text_settings_summary">After the personal signature is turned on, the secondary screen will continue to display the personal signature when it lights up until the secondary screen is operated</string>
    <string name="subscreen_open_race_text_settings_titile">Open personalized signature</string>
    <string name="subscreen_pick_music_app">Select music app</string>
    <string name="subscreen_set_race_text_settings_titile">Personalized signature settings</string>
    <string name="subscreen_setting">Subscreen Settings</string>
    <string name="subscreen_setting_summary">Display Settings, Screen on settings</string>
    <string name="subscreen_show_noti_apps_summary">Select the apps which show notifications in subscreen</string>
    <string name="subscreen_show_noti_apps_title">App select</string>
    <string name="subscreen_take_photo_voice_summary">Voice of take photo</string>
    <string name="subscreen_take_photo_voice_titile">Take photo voice</string>
    <string name="take_photo">Take photo</string>
    <string name="secondary_screen_dashboard_summary">Switch, function selection, function settings</string>
</resources>