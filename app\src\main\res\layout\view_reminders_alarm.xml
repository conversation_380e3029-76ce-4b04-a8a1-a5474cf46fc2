<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/screen_width"
    android:layout_height="@dimen/screen_height"
    android:background="@android:color/black">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44px"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_alarm" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8px"
            android:includeFontPadding="false"
            android:text="@string/alarm_remind"
            android:textColor="@android:color/white"
            android:textSize="20px" />
    </LinearLayout>


    <TextView
        android:id="@+id/tv_current_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="80px"
        android:textColor="@android:color/white"
        android:textSize="32px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.yk.secondary.screen.settings.view.SlideSwitchView
        android:id="@+id/ssv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20px"
        app:center_img="@drawable/slide_switch_view_center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:left_text="@string/delay"
        app:right_text="@string/stop"
        app:view_bg="@drawable/slide_switch_view_bg" />
</androidx.constraintlayout.widget.ConstraintLayout>