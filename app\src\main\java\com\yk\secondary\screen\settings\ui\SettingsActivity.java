package com.yk.secondary.screen.settings.ui;

import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BACK_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_BRIGHTNESS;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_DISPLAY_DURATION;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_FRONT_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_BACK_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_DOUBLE_TOUCH_KEY;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_FRONT_CAMERA_PHOTOSIZE_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_MINISCREEN_MAIN_SWITCH;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_MUSIC_APP_PICKER;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_OPEN_MUSIC_APP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SHOW_NOTI_APPS;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_AOD_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_BACKLIGHT_LEVEL;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CALL_TIP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_CLOCK_SELECT;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_FLIP_SCREENON;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_GOHOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_NOTI_TIP_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_SCREEN_TIMEOUT;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_WITH_MAINSCREEN;
import static com.yk.secondary.screen.settings.utils.Constant.PATH_SECONDARY_BRIGHTNESS_NODES;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.app.ActionBar;
import android.content.Intent;
import android.os.Bundle;
import android.preference.ListPreference;
import android.preference.Preference;
import android.preference.PreferenceActivity;
import android.preference.SwitchPreference;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;
import com.yk.secondary.screen.settings.utils.FileIOUtils;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.utils.Utils;

public class SettingsActivity extends PreferenceActivity implements Preference.OnPreferenceClickListener, Preference.OnPreferenceChangeListener {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        showActionbarBackButton();
        addPreferencesFromResource(R.xml.setting_screen);
        initPreferences();
    }

    private void showActionbarBackButton() {
        ActionBar actionBar = getActionBar();
        if (actionBar != null) {
            actionBar.setTitle(R.string.app_name);
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeButtonEnabled(true);
            actionBar.setElevation(0);
        }
    }

    private void initPreferences() {
        //副屏总开关
        SwitchPreference switchPreference = (SwitchPreference) findPreference(KEY_MINISCREEN_MAIN_SWITCH);
        switchPreference.setOnPreferenceChangeListener(this);
        switchPreference.setChecked(Utils.isOpenSecondaryScreen());
        //表盘选择
        Preference findPreference = findPreference(KEY_SUBSCREEN_CLOCK_SELECT);
        findPreference.setOnPreferenceClickListener(this);

        //屏幕超时
        ListPreference listPreference = (ListPreference) findPreference(KEY_SUBSCREEN_SCREEN_TIMEOUT);
        listPreference.setOnPreferenceChangeListener(this);
        setSubScreenScreenTimeoutSummary(listPreference);

        //背光等级
        ListPreference listPreference2 = (ListPreference) findPreference(KEY_SUBSCREEN_BACKLIGHT_LEVEL);
        listPreference2.setOnPreferenceChangeListener(this);
        setSubScreenBacklightLevelSummary(listPreference2);

        //返回首页时间
        ListPreference listPreference3 = (ListPreference) findPreference(KEY_SUBSCREEN_GOHOME_TIME);
        listPreference3.setOnPreferenceChangeListener(this);
        setSubScreenGoHomeTimeSummary(listPreference3);

        //息屏显示
//        SwitchPreference switchPreference1 = (SwitchPreference) findPreference(KEY_SUBSCREEN_AOD_ENABLE);
//        switchPreference1.setOnPreferenceChangeListener(this);
//        switchPreference1.setChecked(Utils.isAodEnable());

        //随主屏亮屏
        SwitchPreference switchPreference2 = (SwitchPreference) findPreference(KEY_SUBSCREEN_WITH_MAINSCREEN);
        switchPreference2.setOnPreferenceChangeListener(this);
        switchPreference2.setChecked(Utils.isWithMainScreen());

        //翻转亮屏
        SwitchPreference switchPreference10 = (SwitchPreference) findPreference(KEY_SUBSCREEN_FLIP_SCREENON);
        switchPreference10.setOnPreferenceChangeListener(this);
        switchPreference10.setChecked(Utils.isFlipScreenOn());

        //双击亮屏
        SwitchPreference switchPreference11 = (SwitchPreference) findPreference(KEY_DOUBLE_TOUCH_KEY);
        switchPreference11.setOnPreferenceChangeListener(this);
        switchPreference11.setChecked(Utils.isDoubleTouchKeyScreenOn());

        //通话提示
        SwitchPreference switchPreference3 = (SwitchPreference) findPreference(KEY_SUBSCREEN_CALL_TIP_ENABLE);
        switchPreference3.setOnPreferenceChangeListener(this);
        switchPreference3.setChecked(Utils.isCallTipEnable());

        //通知提示
        SwitchPreference switchPreference4 = (SwitchPreference) findPreference(KEY_SUBSCREEN_NOTI_TIP_ENABLE);
        switchPreference4.setOnPreferenceChangeListener(this);
        switchPreference4.setChecked(Utils.isNotiTipEnable());

        //通知应用选择选择
        Preference findPreference2 = findPreference(KEY_SHOW_NOTI_APPS);
        findPreference2.setOnPreferenceClickListener(this);

        //打开音乐应用
        SwitchPreference switchPreference5 = (SwitchPreference) findPreference(KEY_OPEN_MUSIC_APP_ENABLE);
        switchPreference5.setOnPreferenceChangeListener(this);
        switchPreference5.setChecked(Utils.isOpenMusicApp());

        //打开音乐选择
        Preference findPreference1 = findPreference(KEY_MUSIC_APP_PICKER);
        findPreference1.setOnPreferenceClickListener(this);

        //拍照声音
        SwitchPreference switchPreference6 = (SwitchPreference) findPreference(KEY_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE);
        switchPreference6.setOnPreferenceChangeListener(this);
        switchPreference6.setChecked(Utils.isTakePhotoVoiceEnable());

        //后摄拍照大小
        ListPreference listPreference4 = (ListPreference) findPreference(KEY_BACK_CAMERA_PHOTOSIZE_PICKER);
        listPreference4.setOnPreferenceChangeListener(this);
        setBackCameraPhotoSizeSummary(listPreference4);

        //前摄拍照大小
        ListPreference listPreference5 = (ListPreference) findPreference(KEY_FRONT_CAMERA_PHOTOSIZE_PICKER);
        listPreference5.setOnPreferenceChangeListener(this);
        setFrontCameraPhotoSizeSummary(listPreference5);
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {
        if (preference == null) {
            return false;
        }
        String key = preference.getKey();
        Log.i(TAG_LOG, "onPreferenceChange key:" + key);
        if (KEY_MINISCREEN_MAIN_SWITCH.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_MINISCREEN_MAIN_SWITCH ");
            SecondaryScreenControlManager.getInstance().switchSecondaryScreen(booleanValue, () -> Utils.setSecondaryScreenState(booleanValue));
            return true;
        }
        if (KEY_SUBSCREEN_SCREEN_TIMEOUT.equals(key)) {
            String str = (String) newValue;
            int parseInt = Integer.parseInt(str);
            Log.i(TAG_LOG, "onPreferenceChange KEY_SCREEN_TIMEOUT value:" + str + ", time:" + parseInt);
            SPUtils.getInstance().put(KEY_SUBSCREEN_SCREEN_TIMEOUT, parseInt);
            setSubScreenScreenTimeoutSummary((ListPreference) preference);
            return true;
        }
        if (KEY_SUBSCREEN_BACKLIGHT_LEVEL.equals(key)) {
            String str = (String) newValue;
            int parseInt = Integer.parseInt(str);
            Log.i(TAG_LOG, "onPreferenceChange KEY_SUBSCREEN_BACKLIGHT_LEVEL value:" + str + ", time:" + parseInt);
            SPUtils.getInstance().put(KEY_SUBSCREEN_BACKLIGHT_LEVEL, parseInt);
            setSubScreenBacklightLevelSummary((ListPreference) preference);
            boolean writeResult = FileIOUtils.writeFileFromString(PATH_SECONDARY_BRIGHTNESS_NODES, String.valueOf(parseInt));
            Log.i(TAG_LOG, "onProgressChanged: brightnessValue = " + parseInt + " writeResult " + writeResult);
            return true;
        }
        if (KEY_SUBSCREEN_GOHOME_TIME.equals(key)) {
            String str = (String) newValue;
            int parseInt = Integer.parseInt(str);
            Log.i(TAG_LOG, "onPreferenceChange KEY_SUBSCREEN_GOHOME_TIME value:" + str + ", time:" + parseInt);
            SPUtils.getInstance().put(KEY_SUBSCREEN_GOHOME_TIME, parseInt);
            setSubScreenGoHomeTimeSummary((ListPreference) preference);
            return true;
        }
        if (KEY_SUBSCREEN_AOD_ENABLE.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_SUBSCREEN_AOD_ENABLE ");
            Utils.setAodEnable(booleanValue);
            return true;
        }
        if (KEY_SUBSCREEN_WITH_MAINSCREEN.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_SUBSCREEN_WITH_MAINSCREEN ");
            Utils.setWithMainScreen(booleanValue);
            return true;
        }
        if (KEY_SUBSCREEN_CALL_TIP_ENABLE.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_SUBSCREEN_CALL_TIP_ENABLE ");
            Utils.setCallTipEnable(booleanValue);
            return true;
        }
        if (KEY_SUBSCREEN_NOTI_TIP_ENABLE.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_SUBSCREEN_NOTI_TIP_ENABLE ");
            Utils.setNotiTipEnable(booleanValue);
            return true;
        }
        if (KEY_OPEN_MUSIC_APP_ENABLE.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_OPEN_MUSIC_APP_ENABLE ");
            Utils.setOpenMusicApp(booleanValue);
            return true;
        }
        if (KEY_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_OPEN_MUSIC_APP_ENABLE ");
            Utils.setTakePhotoVoiceEnable(booleanValue);
            return true;
        }
        if (KEY_BACK_CAMERA_PHOTOSIZE_PICKER.equals(key)) {
            String str = (String) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_BACK_CAMERA_PHOTOSIZE_PICKER value:" + str);
            SPUtils.getInstance().put(KEY_BACK_CAMERA_PHOTOSIZE_PICKER, str);
            setBackCameraPhotoSizeSummary((ListPreference) preference);
            return true;
        }
        if (KEY_FRONT_CAMERA_PHOTOSIZE_PICKER.equals(key)) {
            String str = (String) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_FRONT_CAMERA_PHOTOSIZE_PICKER value:" + str);
            SPUtils.getInstance().put(KEY_FRONT_CAMERA_PHOTOSIZE_PICKER, str);
            setFrontCameraPhotoSizeSummary((ListPreference) preference);
            return true;
        }
        if (KEY_SUBSCREEN_FLIP_SCREENON.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_SUBSCREEN_FLIP_SCREENON ");
            Utils.setFlipScreenOn(booleanValue);
            return true;
        }
        if (KEY_DOUBLE_TOUCH_KEY.equals(key)) {
            boolean booleanValue = (Boolean) newValue;
            Log.i(TAG_LOG, "onPreferenceChange KEY_DOUBLE_TOUCH_KEY ");
            Utils.setDoubleTouchKeyScreenOn(booleanValue);
            return true;
        }
        return false;
    }

    @Override
    public boolean onPreferenceClick(Preference preference) {
        if (preference == null) {
            return false;
        }
        if (KEY_SUBSCREEN_CLOCK_SELECT.equals(preference.getKey())) {
            startActivity(new Intent(this, ClockSelectActivity.class));
            return true;
        }
        if (KEY_MUSIC_APP_PICKER.equals(preference.getKey())) {
            startActivity(new Intent(this, MusicActivity.class));
            return true;
        }
        if (KEY_SHOW_NOTI_APPS.equals(preference.getKey())) {
            startActivity(new Intent(this, NofiAppsSelectActivity.class));
            return true;
        }
        return false;
    }

    /**
     * 设置屏幕超时副标题
     */
    private void setSubScreenScreenTimeoutSummary(ListPreference listPreference) {
        int time = SPUtils.getInstance().getInt(KEY_SUBSCREEN_SCREEN_TIMEOUT, DEFAULT_DISPLAY_DURATION);
        listPreference.setValue(String.valueOf(time));
        CharSequence[] entryValues = getResources().getStringArray(R.array.screen_timeout_values);
        CharSequence[] entries = getResources().getStringArray(R.array.screen_timeout_entries);
        int index = -1;
        for (int i = 0; i < entryValues.length; i++) {
            if (TextUtils.equals(String.valueOf(time), entryValues[i])) {
                index = i;
            }
        }
        if (index != -1) {
            listPreference.setSummary(entries[index]);
        }
    }

    private void setSubScreenBacklightLevelSummary(ListPreference listPreference) {
        int value = SPUtils.getInstance().getInt(KEY_SUBSCREEN_BACKLIGHT_LEVEL, DEFAULT_BRIGHTNESS);
        listPreference.setValue(String.valueOf(value));
        CharSequence[] entryValues = getResources().getStringArray(R.array.backlight_level_values);
        CharSequence[] entries = getResources().getStringArray(R.array.backlight_level_entries);
        int index = -1;
        for (int i = 0; i < entryValues.length; i++) {
            if (TextUtils.equals(String.valueOf(value), entryValues[i])) {
                index = i;
            }
        }
        if (index != -1) {
            listPreference.setSummary(entries[index]);
        }
    }

    private void setSubScreenGoHomeTimeSummary(ListPreference listPreference) {
        int value = SPUtils.getInstance().getInt(KEY_SUBSCREEN_GOHOME_TIME, DEFAULT_GO_HOME_TIME);
        listPreference.setValue(String.valueOf(value));
        CharSequence[] entryValues = getResources().getStringArray(R.array.screen_gohome_values);
        CharSequence[] entries = getResources().getStringArray(R.array.screen_gohome_entries);
        int index = -1;
        for (int i = 0; i < entryValues.length; i++) {
            if (TextUtils.equals(String.valueOf(value), entryValues[i])) {
                index = i;
            }
        }
        if (index != -1) {
            listPreference.setSummary(entries[index]);
        }
    }

    private void setBackCameraPhotoSizeSummary(ListPreference listPreference) {
        String value = SPUtils.getInstance().getString(KEY_BACK_CAMERA_PHOTOSIZE_PICKER, DEFAULT_BACK_CAMERA_PHOTOSIZE_PICKER);
        listPreference.setValue(value);
        String[] entryValues = getResources().getStringArray(R.array.back_camera_photosize_values);
        String[] entries = getResources().getStringArray(R.array.back_camera_photosize_entries);
        int index = -1;
        for (int i = 0; i < entryValues.length; i++) {
            if (TextUtils.equals(value, entryValues[i])) {
                index = i;
            }
        }
        if (index != -1) {
            listPreference.setSummary(entries[index]);
        }
    }

    private void setFrontCameraPhotoSizeSummary(ListPreference listPreference) {
        String value = SPUtils.getInstance().getString(KEY_FRONT_CAMERA_PHOTOSIZE_PICKER, DEFAULT_FRONT_CAMERA_PHOTOSIZE_PICKER);
        listPreference.setValue(value);
        String[] entryValues = getResources().getStringArray(R.array.front_camera_photosize_values);
        String[] entries = getResources().getStringArray(R.array.front_camera_photosize_entries);
        int index = -1;
        for (int i = 0; i < entryValues.length; i++) {
            if (TextUtils.equals(value, entryValues[i])) {
                index = i;
            }
        }
        if (index != -1) {
            listPreference.setSummary(entries[index]);
        }
    }

}
