package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_DISPLAY_DURATION;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_SCREEN_TIMEOUT;
import static com.yk.secondary.screen.settings.utils.Constant.PATH_SECONDARY_SCREEN_NODES;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.yk.secondary.screen.settings.utils.FileIOUtils;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;
import com.yk.secondary.screen.settings.utils.Utils;

public class SecondaryScreenControlManager implements Handler.Callback {

    public static final int STATUS_SCREEN_OPEN = 0;//打开、亮屏
    public static final int STATUS_SCREEN_CLOSE = 1;//关闭、灭屏
    public static final int STATUS_SCREEN_OPEN_IN = 2;//打开、亮屏过程中
    public static final int STATUS_SCREEN_CLOSE_IN = 3;//关闭、灭屏过程中

    private final Handler mHandler;

    private final int MSG_GO_TO_SLEEP = 100;

    //屏状态
    private int mScreenStatus = STATUS_SCREEN_CLOSE;

    public int getScreenStatus() {
        return mScreenStatus;
    }

    private SecondaryScreenControlManager() {
        mHandler = new Handler(Looper.getMainLooper(), this);
    }

    private static class SingletonHolder {
        private static final SecondaryScreenControlManager instance = new SecondaryScreenControlManager();
    }

    public static SecondaryScreenControlManager getInstance() {
        return SingletonHolder.instance;
    }

    @Override
    public boolean handleMessage(@NonNull Message msg) {
        if (msg.what == MSG_GO_TO_SLEEP) {
            Log.i(TAG_LOG, "SecondaryScreenControlManager handleMessage: MSG_GO_TO_SLEEP ");
            //关闭副屏
            switchSecondaryScreen(false, null);
        }
        return false;
    }

    public void removeSecondaryScreenSleepMsg() {
        Log.i(TAG_LOG, "SecondaryScreenControlManager removeSecondaryScreenSleepMsg: ");
        if (mHandler != null && mHandler.hasMessages(MSG_GO_TO_SLEEP)) {
            Log.i(TAG_LOG, "SecondaryScreenControlManager start removeSecondaryScreenSleepMsg: ");
            mHandler.removeMessages(MSG_GO_TO_SLEEP);
        }
    }

    public void addSecondaryScreenSleepMsg() {
        if (FactoryTestManager.getInstance().isFactoryTest() || (RemindersViewManager.getInstance().isShow() && !(TextUtils.equals(TelephonyManager.EXTRA_STATE_OFFHOOK, RemindersViewManager.getInstance().getPhotoState())))) {
            return;
        }

        if (mHandler != null) {
            Log.i(TAG_LOG, "SecondaryScreenControlManager addSecondaryScreenSleepMsg: ");
            mHandler.removeMessages(MSG_GO_TO_SLEEP);
            mHandler.sendEmptyMessageDelayed(MSG_GO_TO_SLEEP, SPUtils.getInstance().getInt(KEY_SUBSCREEN_SCREEN_TIMEOUT, DEFAULT_DISPLAY_DURATION));
        }
    }

    /**
     * 副屏开关，指是否关闭副屏
     */
    public synchronized void switchSecondaryScreen(boolean isOn, OnSwitchSecondaryScreenCallback callback) {
        Log.i(TAG_LOG, "switchSecondaryScreen: isOn " + isOn);
        if (isOn && (mScreenStatus == STATUS_SCREEN_OPEN_IN || mScreenStatus == STATUS_SCREEN_OPEN)) {
            Log.i(TAG_LOG, "switchSecondaryScreen: isOn is true 副屏正在打开或者已经打开 ");
            return;
        }

        if (!isOn && (mScreenStatus == STATUS_SCREEN_CLOSE_IN || mScreenStatus == STATUS_SCREEN_CLOSE)) {
            Log.i(TAG_LOG, "switchSecondaryScreen: isOn is false 副屏正在关闭或者已经关闭 ");
            return;
        }

        //总开关关闭
        if (isOn && !Utils.isOpenSecondaryScreen()) {
            Log.i(TAG_LOG, "switchSecondaryScreen: 副屏总开关关闭 ");
            return;
        }

        if (FactoryTestManager.getInstance().isFactoryTest() && !isOn) {
            Log.i(TAG_LOG, "switchSecondaryScreen: 工厂测试中，不能关闭副屏.... ");
            return;
        }

        Log.i(TAG_LOG, "switchSecondaryScreen: RemindersViewManager  isShow " + RemindersViewManager.getInstance().isShow());
        Log.i(TAG_LOG, "switchSecondaryScreen: RemindersViewManager  getPhotoState " + RemindersViewManager.getInstance().getPhotoState());
        if ((RemindersViewManager.getInstance().isShow() &&
                !(TextUtils.equals(TelephonyManager.EXTRA_STATE_OFFHOOK,
                        RemindersViewManager.getInstance().getPhotoState()))
                && !isOn)) {
            Log.i(TAG_LOG, "switchSecondaryScreen: ，来电或者闹钟界面 不能关闭副屏.... ");
            return;
        }

        if (isOn) {
            mScreenStatus = STATUS_SCREEN_OPEN_IN;
            SecondaryScreenBitmapManager.getInstance().init();
        } else {
            SecondaryScreenBitmapManager.getInstance().releaseWakeLock();
            mScreenStatus = STATUS_SCREEN_CLOSE_IN;
        }

        if (!isOn) {
            SecondaryScreenBitmapManager.getInstance().setIsRefreshSecondaryScreenImage(false);
            if (SecondaryScreenBitmapManager.getInstance().getIsWriteBitmapDataToLcd()) {
                Log.i(TAG_LOG, "switchSecondaryScreen doInBackground: IsWriteBitmapDataToLcd is true .... ");
                while (!SecondaryScreenBitmapManager.getInstance().getBlackBgSuccess()) {
                    SystemClock.sleep(10);
                }
            }
        }

        boolean isSuccess = false;
        for (int i = 0; i < 5; i++) {
            Log.i(TAG_LOG, "11 doInBackground: start to " + (isOn ? "open" : "close") + " the sublcd in " + i);
            isSuccess = FileIOUtils.writeFileFromString(PATH_SECONDARY_SCREEN_NODES, isOn ? "1" : "0");
            Log.i(TAG_LOG, "22 doInBackground: write spi_lcm_enable is isSuccess? " + isSuccess + ", times = " + i);
            if (isSuccess) {
                break;
            }
            SystemClock.sleep(10);
        }

        Log.i(TAG_LOG, "switchSecondaryScreen: isSuccess " + isSuccess);
        if (isSuccess) {
            if (isOn) {
                mScreenStatus = STATUS_SCREEN_OPEN;
            } else {
                mScreenStatus = STATUS_SCREEN_CLOSE;
                removeSecondaryScreenSleepMsg();
            }

            Log.i(TAG_LOG, "switchSecondaryScreen 副屏" + (isOn ? "打开成功" : "关闭成功"));
            if (isOn) {
                addSecondaryScreenSleepMsg();
            } else {
                SecondaryScreenBitmapManager.getInstance().onDestroy();
            }

            if (callback != null) {
                callback.onSwitchSuccess();
            }
        } else {
            if (mScreenStatus == STATUS_SCREEN_OPEN_IN) {
                mScreenStatus = STATUS_SCREEN_CLOSE;
                SecondaryScreenBitmapManager.getInstance().setIsRefreshSecondaryScreenImage(false);
            }

            if (mScreenStatus == STATUS_SCREEN_CLOSE_IN) {
                mScreenStatus = STATUS_SCREEN_OPEN;
            }
        }
    }

    public interface OnSwitchSecondaryScreenCallback {
        void onSwitchSuccess();
    }
}
