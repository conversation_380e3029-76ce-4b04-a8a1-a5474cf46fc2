package com.yk.secondary.screen.settings.view.watch;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;
import static com.yk.secondary.screen.settings.utils.TimeUtils.getSafeDateFormat;

import android.util.Log;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.entity.BatteryState;
import com.yk.secondary.screen.settings.manager.SecondaryScreenBitmapManager;
import com.yk.secondary.screen.settings.utils.TimeUtils;
import com.yk.secondary.screen.settings.utils.Utils;

import java.util.Calendar;

public class WatchStyle10 extends BaseWatchStyle {

    private ImageView mIvDay1, mIvDay2, mIvMonth1, mIvMonth2, mIvWeek, mIvBattery;

    public WatchStyle10() {
        super();
    }

    @Override
    public void initViews() {
        mRootView = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_bitmap_watch_style10, null);

        mIvWeek = mRootView.findViewById(R.id.iv_week);
        setWeek();

        mIvBattery = mRootView.findViewById(R.id.iv_battery);

        mIvDay1 = mRootView.findViewById(R.id.iv_day_1);
        mIvDay2 = mRootView.findViewById(R.id.iv_day_2);
        setDay();

        mIvMonth1 = mRootView.findViewById(R.id.iv_month_1);
        mIvMonth2 = mRootView.findViewById(R.id.iv_month_2);
        setMonth();
    }

    @Override
    public int getStyleValue() {
        return 10;
    }

    @Override
    public void updateTimeChanged() {
        setWeek();
        setDay();
        setMonth();
    }

    @Override
    public void updateDateChanged() {
        setWeek();
        setDay();
        setMonth();
    }

    @Override
    public void updateCurrentLevelChanged(BatteryState batteryState) {
        setBattery(batteryState.getLevel());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void setDay() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("dd"));
        try {
            if (mIvDay1 != null) {
                mIvDay1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvDay2 != null) {
                mIvDay2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setDays: Exception " + e.getMessage());
        }
    }

    private void setMonth() {
        String days = TimeUtils.millis2String(System.currentTimeMillis(), getSafeDateFormat("MM"));
        try {
            if (mIvMonth1 != null) {
                mIvMonth1.setImageResource(getDateImageResource(days.charAt(0)));
            }

            if (mIvMonth2 != null) {
                mIvMonth2.setImageResource(getDateImageResource(days.charAt(1)));
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setMonth: Exception " + e.getMessage());
        }
    }

    private void setWeek() {
        if (mIvWeek != null) {
            mIvWeek.setImageResource(getWeekImageResource());
        }
    }

    private void setBattery(int level) {
        if (mIvBattery != null) {
            mIvBattery.setImageResource(getBatteryImageResource(level));
        }
    }

    private int getDateImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_watch_style10_date_0;
            case '1':
                return R.drawable.icon_watch_style10_date_1;
            case '2':
                return R.drawable.icon_watch_style10_date_2;
            case '3':
                return R.drawable.icon_watch_style10_date_3;
            case '4':
                return R.drawable.icon_watch_style10_date_4;
            case '5':
                return R.drawable.icon_watch_style10_date_5;
            case '6':
                return R.drawable.icon_watch_style10_date_6;
            case '7':
                return R.drawable.icon_watch_style10_date_7;
            case '8':
                return R.drawable.icon_watch_style10_date_8;
            case '9':
                return R.drawable.icon_watch_style10_date_9;
            default:
                return R.drawable.icon_watch_style10_date_0;
        }
    }

    private int getBatteryImageResource(int level) {
        if (level <= 0) {
            return R.drawable.icon_watch_style10_battery_0;
        } else if (level <= 10) {
            return R.drawable.icon_watch_style10_battery_10;
        } else if (level <= 20) {
            return R.drawable.icon_watch_style10_battery_20;
        } else if (level <= 30) {
            return R.drawable.icon_watch_style10_battery_30;
        } else if (level <= 40) {
            return R.drawable.icon_watch_style10_battery_40;
        } else if (level <= 50) {
            return R.drawable.icon_watch_style10_battery_50;
        } else if (level <= 60) {
            return R.drawable.icon_watch_style10_battery_60;
        } else if (level <= 70) {
            return R.drawable.icon_watch_style10_battery_70;
        } else if (level <= 80) {
            return R.drawable.icon_watch_style10_battery_80;
        } else if (level <= 90) {
            return R.drawable.icon_watch_style10_battery_90;
        } else if (level >= 100) {
            return R.drawable.icon_watch_style10_battery_100;
        }
        return R.drawable.icon_watch_style10_battery_100;
    }

    private int getWeekImageResource() {
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        boolean isChinese = Utils.isChinese(App.getInstance().getContext());
        switch (dayOfWeek) {
            case 1:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_sun_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_sun_en;
                }
            case 2:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_mon_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_mon_en;
                }
            case 3:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_tue_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_tue_en;
                }
            case 4:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_wed_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_wed_en;
                }
            case 5:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_thu_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_thu_en;
                }
            case 6:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_fri_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_fri_en;
                }
            case 7:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_sat_en;
                }
            default:
                if (isChinese) {
                    return R.drawable.icon_watch_style10_week_sat_zh;
                } else {
                    return R.drawable.icon_watch_style10_week_sat_en;
                }
        }
    }
}
