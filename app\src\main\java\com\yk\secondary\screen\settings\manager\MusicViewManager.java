package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.graphics.drawable.VectorDrawable;
import android.os.Bundle;
import android.service.notification.StatusBarNotification;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RemoteViews;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.utils.ImageUtils;
import com.yk.secondary.screen.settings.utils.Utils;
import com.yk.secondary.screen.settings.view.MainHorizontalScrollView;
import com.yk.secondary.screen.settings.view.MusicMarqueeTextView;

public class MusicViewManager {
    private MusicViewManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final MusicViewManager instance = new MusicViewManager();
    }

    public static MusicViewManager getInstance() {
        return MusicViewManager.SingletonHolder.instance;
    }

    private FrameLayout mRootView;

    private MainHorizontalScrollView mMainHorizontalScrollView;

    private ImageButton mIbPre, mIbPlayPause, mIbNext;

    private MusicMarqueeTextView mTvMusicName;

    private StatusBarNotification mStatusBarNotification;

    public void setYkHorizontalScrollView(MainHorizontalScrollView mMainHorizontalScrollView) {
        this.mMainHorizontalScrollView = mMainHorizontalScrollView;
    }

    @SuppressLint("InflateParams")
    public View createMusicView() {
        Log.i(TAG_LOG, "MusicViewManager createMusicView: ");
        if (mRootView == null) {
            mRootView = (FrameLayout) SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_root_main, null);
        }
        View view = SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_music, null);
        initViews(view);
        mRootView.removeAllViews();
        mRootView.addView(view);
        return mRootView;
    }

    private void initViews(View view) {
        mIbPre = view.findViewById(R.id.ib_play_pre);
        mIbPlayPause = view.findViewById(R.id.ib_play_pause);
        mIbNext = view.findViewById(R.id.ib_play_next);

        mTvMusicName = view.findViewById(R.id.tv_music_name);
        updateMusicInfo();
    }

    public void onUpdateMusicNotification(StatusBarNotification statusBarNotification) {
        Log.i(TAG_LOG, "MusicViewManager onUpdateMusicNotification: ");
        mStatusBarNotification = statusBarNotification;
        if (mRootView == null) {
            return;
        }
        updateMusicInfo();
    }

    public void initMusicView() {
        Log.i(TAG_LOG, "MusicViewManager initMusicView: ");
        mStatusBarNotification = null;
        updateMusicInfo();
    }

    private void updateMusicInfo() {
        if (mStatusBarNotification == null) {
            if (mTvMusicName != null) {
                mTvMusicName.setText("");
            }

            if (mIbPre != null) {
                mIbPre.setImageResource(R.drawable.icon_music_pre);
            }
            if (mIbPlayPause != null) {
                mIbPlayPause.setImageResource(R.drawable.icon_music_play);
                if (mMainHorizontalScrollView != null) {
                    mMainHorizontalScrollView.setOnClickListener(mIbPlayPause, id -> {
                        if (id == R.id.ib_play_pause) {
                            if (!Utils.isDeviceProvisioned(App.getInstance().getContext())) {
                                Log.i(TAG_LOG, "MusicViewManager: isDeviceProvisioned ...........");
                                return;
                            }

                            if (mStatusBarNotification == null && Utils.isOpenMusicApp()) {
                                Intent intent = getIntent();
                                App.getInstance().getContext().startActivity(intent);
                            }
                        }
                    });
                }
            }
            if (mIbNext != null) {
                mIbNext.setImageResource(R.drawable.icon_music_next);
            }
            return;
        }

        Notification notification = mStatusBarNotification.getNotification();
        if (notification == null) {
            return;
        }

        Bundle bundle = notification.extras;
        if (bundle == null) {
            return;
        }

        if (!TextUtils.equals(mStatusBarNotification.getPackageName(), "com.netease.cloudmusic")) {
            CharSequence title = bundle.getCharSequence(Notification.EXTRA_TITLE);
            CharSequence text = bundle.getCharSequence(Notification.EXTRA_TEXT);
            if (mTvMusicName != null && !TextUtils.equals(title, mTvMusicName.getText())) {
                mTvMusicName.setText(title);
                mTvMusicName.startScroll();
            }
        }

        initMusicButton();
    }

    private @NonNull Intent getIntent() {
        Intent intent = new Intent();
        intent.setPackage(Utils.getPickedMusicAppPkgName());
        intent.setAction(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        return intent;
    }

    private void initMusicButton() {
        if (mStatusBarNotification == null) {
            return;
        }

        if (mStatusBarNotification.getPackageName().equals("com.netease.cloudmusic")) {

        } else {
            Notification.Action[] actions = mStatusBarNotification.getNotification().actions;
            if (actions == null || actions.length == 0) {
                return;
            }
            String packageName = mStatusBarNotification.getPackageName();
            if (mMainHorizontalScrollView != null) {
                mMainHorizontalScrollView.setOnClickListener(mIbPre, id -> {
                    if (id == R.id.ib_play_pre) {//上一曲
                        try {
                            if ("com.google.android.apps.youtube.music".equals(packageName)) {
                                if (actions.length == 3 || actions.length == 4) {
                                    actions[0].actionIntent.send();
                                } else if (actions.length == 5) {
                                    actions[1].actionIntent.send();
                                }
                            }

                            if ("com.prize.music".equals(packageName) || "com.luna.music".equals(packageName)) {
                                actions[0].actionIntent.send();
                            }
                            if ("com.tencent.qqmusic".equals(packageName) || "com.kugou.android".equals(packageName) || "cn.wenyu.bodian".equals(packageName)) {
                                actions[1].actionIntent.send();
                            }
                        } catch (Exception e) {
                            Log.i(TAG_LOG, "initMusicButton: ib_play_pre " + e.getMessage());
                        }
                    }
                });

                mMainHorizontalScrollView.setOnClickListener(mIbPlayPause, id -> {
                    if (id == R.id.ib_play_pause) {//播放暂停
                        try {
                            if ("com.google.android.apps.youtube.music".equals(packageName)) {
                                if (actions.length == 3 || actions.length == 4) {
                                    actions[1].actionIntent.send();
                                } else if (actions.length == 5) {
                                    actions[2].actionIntent.send();
                                }
                            }

                            if ("com.prize.music".equals(packageName) || "com.luna.music".equals(packageName)) {
                                actions[1].actionIntent.send();
                            }
                            if ("com.tencent.qqmusic".equals(packageName) || "com.kugou.android".equals(packageName) || "cn.wenyu.bodian".equals(packageName)) {
                                actions[2].actionIntent.send();
                            }
                        } catch (Exception e) {
                            Log.i(TAG_LOG, "initMusicButton: ib_play_pre " + e.getMessage());
                        }
                    }
                });

                mMainHorizontalScrollView.setOnClickListener(mIbNext, id -> {
                    if (id == R.id.ib_play_next) {//下一曲
                        try {
                            if ("com.google.android.apps.youtube.music".equals(packageName)) {
                                if (actions.length == 3 || actions.length == 4) {
                                    actions[2].actionIntent.send();
                                } else if (actions.length == 5) {
                                    actions[3].actionIntent.send();
                                }
                            }

                            if ("com.prize.music".equals(packageName) || "com.luna.music".equals(packageName)) {
                                actions[2].actionIntent.send();
                            }
                            if ("com.tencent.qqmusic".equals(packageName) || "com.kugou.android".equals(packageName) || "cn.wenyu.bodian".equals(packageName)) {
                                actions[3].actionIntent.send();
                            }
                        } catch (Exception e) {
                            Log.i(TAG_LOG, "initMusicButton: ib_play_pre " + e.getMessage());
                        }
                    }
                });
            }
        }

        try {
            LayoutInflater layoutInflater = LayoutInflater.from(App.getInstance().getContext());
            FrameLayout frameLayout = new FrameLayout(App.getInstance().getContext());
            Notification.Builder builder = Notification.Builder.recoverBuilder(App.getInstance().getContext(), mStatusBarNotification.getNotification());
            RemoteViews remoteViews;
            if (mStatusBarNotification.getPackageName().equals("com.netease.cloudmusic")) {
                remoteViews = builder.createBigContentView();
            } else {
                remoteViews = builder.createContentView();
            }
            FrameLayout mFrameLayout1 = (FrameLayout) layoutInflater.inflate(remoteViews.getLayoutId(), frameLayout);
            FrameLayout notificationRootView = (FrameLayout) remoteViews.apply(App.getInstance().getContext(), mFrameLayout1);

            getMediaActions(notificationRootView);
        } catch (Exception e) {
            Log.e(TAG_LOG, "MusicViewManager initMusicButton: Exception " + e.getMessage());
        }
    }

    private void getMediaActions(ViewGroup viewGroup) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof ViewGroup) {
                if (view instanceof LinearLayout && view.toString().contains("android:id/media_actions")) {
                    for (int j = 0; j < ((LinearLayout) view).getChildCount(); j++) {
                        View childView = ((LinearLayout) view).getChildAt(j);
                        if (childView instanceof ImageButton) {
                            String packageName = mStatusBarNotification.getPackageName();
                            String name = App.getInstance().getContext().getResources().getResourceName(childView.getId());
                            Drawable drawable = ((ImageButton) childView).getDrawable();
                            if (TextUtils.equals("android:id/action0", name)) {
                                if ("com.kugou.android".equals(packageName)) {
                                    if (drawable instanceof StateListDrawable) {
                                        mIbPlayPause.setScaleType(ImageView.ScaleType.CENTER);
                                        mIbPlayPause.setImageBitmap(ImageUtils.scale(ImageUtils.stateListDrawableToBitmap((StateListDrawable) drawable), 30, 30, false));
                                    }
                                }

                                if ("com.luna.music".equals(packageName)) {
                                    if (drawable instanceof BitmapDrawable) {
                                        mIbPlayPause.setImageBitmap(((BitmapDrawable) drawable).getBitmap());
                                    }
                                }
                                mIbPlayPause.setColorFilter(Color.WHITE);
                            } else if (TextUtils.equals("android:id/action1", name)) {
                                if ("com.prize.music".equals(packageName)) {
                                    if (drawable instanceof VectorDrawable) {
                                        mIbPlayPause.setImageBitmap(ImageUtils.vectorToBitmap((VectorDrawable) drawable, 44, 44));
                                    }
                                }

                                if ("com.tencent.qqmusic".equals(packageName) || "cn.wenyu.bodian".equals(packageName) || "com.google.android.apps.youtube.music".equals(packageName)) {
                                    if (drawable instanceof BitmapDrawable) {
                                        mIbPlayPause.setScaleType(ImageView.ScaleType.CENTER);
                                        mIbPlayPause.setImageBitmap(ImageUtils.scale(((BitmapDrawable) drawable).getBitmap(), 30, 30, false));
                                    }
                                }

                                mIbPlayPause.setColorFilter(Color.WHITE);
                            }
                        }
                    }
                    break;
                }
                getMediaActions((ViewGroup) view);
            } else {
                if (mStatusBarNotification.getPackageName().equals("com.netease.cloudmusic")) {
                    if (view instanceof TextView) {
                        if (view.toString().contains("app:id/notifyTitle")) {
                            CharSequence title = ((TextView) view).getText();
                            if (mTvMusicName != null && !TextUtils.isEmpty(title) && !TextUtils.equals(title, mTvMusicName.getText())) {
                                mTvMusicName.setText(title);
                                mTvMusicName.startScroll();
                            }
                        }
                    }

                    if (view instanceof ImageView) {
                        if (view.toString().contains("app:id/playNotificationToggle")) {
                            Drawable drawable = ((ImageView) view).getDrawable();
                            if (drawable instanceof BitmapDrawable) {
                                mIbPlayPause.setScaleType(ImageView.ScaleType.CENTER);
                                mIbPlayPause.setImageBitmap(ImageUtils.scale(((BitmapDrawable) drawable).getBitmap(), 30, 30, false));
                            }

                            mIbPlayPause.setColorFilter(Color.WHITE);
                            if (mMainHorizontalScrollView != null) {
                                mMainHorizontalScrollView.setOnClickListener(mIbPlayPause, id -> {
                                    if (id == R.id.ib_play_pause) {
                                        view.callOnClick();
                                    }
                                });
                            }
                        }
                        if (view.toString().contains("app:id/playNotificationNext")) {
                            if (mMainHorizontalScrollView != null) {
                                mMainHorizontalScrollView.setOnClickListener(mIbNext, id -> {
                                    if (id == R.id.ib_play_next) {//下一曲
                                        view.callOnClick();
                                    }
                                });
                            }
                        }

                        if (view.toString().contains("app:id/playNotificationPre")) {
                            if (mMainHorizontalScrollView != null) {
                                mMainHorizontalScrollView.setOnClickListener(mIbPre, id -> {
                                    if (id == R.id.ib_play_pre) {//上一曲
                                        view.callOnClick();
                                    }
                                });
                            }
                        }
                    }
                }
            }
        }
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "MusicViewManager onDestroy: ");
        if (mRootView != null) {
            mRootView.removeAllViews();
            mRootView = null;
        }
    }
}
