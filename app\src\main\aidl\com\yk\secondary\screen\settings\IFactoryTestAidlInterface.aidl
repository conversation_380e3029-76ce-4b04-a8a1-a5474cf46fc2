// IFactoryTestAidlInterface.aidl
package com.yk.secondary.screen.settings;

import com.yk.secondary.screen.settings.IRegisterResultCallback;

// Declare any non-default types here with import statements

interface IFactoryTestAidlInterface {
    /**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
    void startFactory();

    void registerResultCallback(IRegisterResultCallback callback);
}