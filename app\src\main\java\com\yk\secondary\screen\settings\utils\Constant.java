package com.yk.secondary.screen.settings.utils;

public class Constant {

    public static final String TAG_LOG = "inCar_Screen";


    public static final int SCREEN_WIDTH = 240;//副屏屏幕的宽

    public static final int SCREEN_HEIGHT = 284;//副屏屏幕的高

    public static final String PATH_HYN_GESTURE_MODE = "/sys/devices/platform/soc/soc:ap-apb/20100000.i2c/i2c-3/3-006a/hyn_gesture_mode";
    public static final String PATH_SPI_LCM_FRAME_DATA = "/sys/devices/platform/soc/soc:ap-apb/20140000.spi/spi_master/spi2/spi2.0/spi_lcm_frame_data";
    public static final String PATH_SECONDARY_SCREEN_NODES = "/sys/devices/platform/soc/soc:ap-apb/20140000.spi/spi_master/spi2/spi2.0/spi_lcm_enable";
    public static final String PATH_SECONDARY_BRIGHTNESS_NODES = "/sys/devices/platform/gpio_backlight/backlight/gpio_backlight/brightness";

    public static final String KEY_MINISCREEN_MAIN_SWITCH = "miniscreen_main_switch";//总开关
    public static final String KEY_SUBSCREEN_CLOCK_SELECT = "subscreen_clock_select";//表盘选择
    public static final String KEY_MUSIC_APP_PICKER = "music_app_picker";//音乐选择
    public static final String KEY_SHOW_NOTI_APPS = "show_noti_apps";//通知应用选择
    public static final String KEY_SUBSCREEN_SCREEN_TIMEOUT = "subscreen_screen_timeout";//显示时长
    public static final String KEY_SUBSCREEN_BACKLIGHT_LEVEL = "subscreen_backlight_level";//亮度
    public static final String KEY_SUBSCREEN_WITH_MAINSCREEN = "subscreen_with_mainscreen";//亮屏方式:跟随主屏
    public static final String KEY_SUBSCREEN_FLIP_SCREENON = "subscreen_flip_screenon";//亮屏方式:翻转亮屏
    public static final String KEY_DOUBLE_TOUCH_KEY = "double_touch_key";//亮屏方式:双击
    public static final String KEY_SUBSCREEN_GOHOME_TIME = "subscreen_gohome_time";//返回首页时间
    public static final String KEY_SUBSCREEN_AOD_ENABLE = "subscreen_aod_enable";//息屏显示
    public static final String KEY_SUBSCREEN_CALL_TIP_ENABLE = "subscreen_call_tip_enable";//通话提示
    public static final String KEY_SUBSCREEN_NOTI_TIP_ENABLE = "subscreen_noti_tip_enable";//通知提示
    public static final String KEY_OPEN_MUSIC_APP_ENABLE = "open_music_app_enable";//打开音乐应用
    public static final String KEY_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE = "subscreen_take_photo_voice_enable";//拍照声音
    public static final String KEY_BACK_CAMERA_PHOTOSIZE_PICKER = "back_camera_photosize_picker";//后摄拍照大小
    public static final String KEY_FRONT_CAMERA_PHOTOSIZE_PICKER = "front_camera_photosize_picker";//前摄拍照大小
    public static final String KEY_PICKED_MUSIC_APP = "picked_music_app";//副屏音乐界面，播放按钮打开应用包名
    public static final String KEY_SUBSCREEN_SELECTED_NOTI_APPS = "subscreen_selected_noti_apps";//通知选中APP列表

    public static final int BRIGHTNESS_LEVEL1 = 1;
    public static final int BRIGHTNESS_LEVEL2 = 2;
    public static final int BRIGHTNESS_LEVEL3 = 4;
    public static final int BRIGHTNESS_LEVEL4 = 6;
    public static final int BRIGHTNESS_LEVEL5 = 8;
    public static final int BRIGHTNESS_LEVEL6 = 10;
    public static final int BRIGHTNESS_LEVEL7 = 12;

    public static final boolean DEFAULT_MATER_SWITCH = true;//总开关默认值，打开
    public static final boolean DEFAULT_AOD_ENABLE = false;//息屏显示默认值，false
    public static final int DEFAULT_WATCH_SELECTION = 0;//表盘默认选择项
    public static final boolean DEFAULT_SUBSCREEN_CALL_TIP_ENABLE = true;//通话提示默认值，true
    public static final boolean DEFAULT_SUBSCREEN_NOTI_TIP_ENABLE = false;//通知提示，false
    public static final boolean DEFAULT_OPEN_MUSIC_APP_ENABLE = true;//打开音乐应用，true
    public static final boolean DEFAULT_SUBSCREEN_TAKE_PHOTO_VOICE_ENABLE = true;//拍照声音，true
    public static final boolean DEFAULT_BRIGHT_SCREEN_MODE_HOME = true;//亮屏方式:跟随主屏 默认值，打开
    public static final boolean DEFAULT_BRIGHT_SCREEN_MODE_FLIP = false;//亮屏方式:翻转亮屏 默认值，关闭
    public static final boolean DEFAULT_BRIGHT_SCREEN_MODE_DOUBLE = true;//亮屏方式:双击 默认值，关闭
    public static final int DEFAULT_DISPLAY_DURATION = 30000;//显示时长默认值
    public static final int DEFAULT_GO_HOME_TIME = 15000;//返回首页默认值
    public static final String DEFAULT_BACK_CAMERA_PHOTOSIZE_PICKER = "4160x3120";//后摄默认照片大小
    public static final String DEFAULT_FRONT_CAMERA_PHOTOSIZE_PICKER = "2592x1944";//前摄默认照片大小
    public static final int DEFAULT_BRIGHTNESS = BRIGHTNESS_LEVEL6;//默认亮度值
    public static final String DEFAULT_MUSIC_PACKAGE_NAME = "com.google.android.apps.youtube.music";//副屏音乐播放按钮默认打开应用包名
    public static final String DEFAULT_SUBSCREEN_SELECTED_NOTI_APPS = "com.android.incall:com.android.deskclock:com.android.mms:com.google.android.calendar:com.google.android.keep:com.mediatek.notebook:com.google.android.dialer:com.google.android.apps.messaging:com.google.android.gm:com.google.android.apps.tachyon:com.tencent.mm:com.tencent.mobileqq:com.android.dialer:com.android.email:com.coolapk.market";//通知默认选中APP列表

    public static final int MSG_GO_HOME_TIME = 1;
}
