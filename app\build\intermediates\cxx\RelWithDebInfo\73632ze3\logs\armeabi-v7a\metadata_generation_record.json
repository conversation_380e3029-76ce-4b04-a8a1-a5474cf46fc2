[{"level_": 0, "message_": "Start JSON generation. Platform version: 34 min SDK version: armeabi-v7a", "file_": "G:\\BitmapSecondaryScreen\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'G:\\BitmapSecondaryScreen\\app\\.cxx\\RelWithDebInfo\\73632ze3\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "G:\\BitmapSecondaryScreen\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "G:\\BitmapSecondaryScreen\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]