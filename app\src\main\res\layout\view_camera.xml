<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/screen_width"
    android:background="@android:color/black"
    android:layout_height="@dimen/screen_height">

    <ImageView
        android:id="@+id/preview"
        android:layout_width="@dimen/screen_width"
        android:layout_height="@dimen/screen_height" />

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:src="@drawable/ic_back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_camera_thumbnail"
        android:layout_width="36px"
        android:layout_height="36px"
        android:layout_marginStart="15px"
        android:layout_marginBottom="15px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/iv_camera_switch"
        android:layout_width="36px"
        android:layout_height="36px"
        android:layout_marginEnd="15px"
        android:layout_marginBottom="15px"
        android:src="@drawable/ic_camera_switch"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/btn_shutter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15px"
        android:src="@drawable/ic_shutter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>