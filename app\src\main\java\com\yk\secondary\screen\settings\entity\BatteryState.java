package com.yk.secondary.screen.settings.entity;

import android.os.Parcel;
import android.os.Parcelable;

public class BatteryState implements Parcelable {

    private int level;
    private boolean charging;

    public BatteryState() {
    }

    public BatteryState(int level, boolean charging) {
        this.level = level;
        this.charging = charging;
    }

    protected BatteryState(Parcel in) {
        level = in.readInt();
        charging = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(level);
        dest.writeByte((byte) (charging ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<BatteryState> CREATOR = new Creator<BatteryState>() {
        @Override
        public BatteryState createFromParcel(Parcel in) {
            return new BatteryState(in);
        }

        @Override
        public BatteryState[] newArray(int size) {
            return new BatteryState[size];
        }
    };

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean isCharging() {
        return charging;
    }

    public void setCharging(boolean charging) {
        this.charging = charging;
    }
}
