package com.yk.secondary.screen.settings.view;

import static android.view.MotionEvent.ACTION_CANCEL;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;

import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.manager.CameraManager;
import com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager;

import java.util.HashMap;

public class CameraFrameLayout extends FrameLayout implements LifecycleOwner {

    private LifecycleRegistry mLifecycleRegistry;

    private GestureDetector mGestureDetector;

    private final HashMap<Integer, OnClickListener> mClickViews = new HashMap<>();

    public void clearClickListener() {
        mClickViews.clear();
    }

    public CameraFrameLayout(@NonNull Context context) {
        super(context);
        init();
        initDoubleTapListener(context);
    }

    public CameraFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
        initDoubleTapListener(context);
    }

    public CameraFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
        initDoubleTapListener(context);
    }

    private void initDoubleTapListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapUp(@NonNull MotionEvent e) {
                Log.i(TAG_LOG, "CameraFrameLayout onSingleTapUp: ");
                try {
                    float x = e.getX();
                    float y = e.getY();
                    if (mClickViews.isEmpty()) {
                        return super.onSingleTapUp(e);
                    }
                    mClickViews.forEach((id, listener) -> {
                        if (id == R.id.btn_back) {//返回
                            if (x >= 87 && x <= 151 && y >= 0 && y <= 66) {
                                listener.onClick(id);
                            }
                        } else if (id == R.id.btn_shutter) {//拍照
                            if (x >= 87 && x <= 151 && y >= 218 && y <= 284) {
                                listener.onClick(id);
                            }
                        } else if (id == R.id.iv_camera_thumbnail) {//查看照片
                            if (x >= 0 && x <= 66 && y >= 218 && y <= 284) {
                                listener.onClick(id);
                            }
                        } else if (id == R.id.iv_camera_switch) {//切换摄像头
                            if (x >= 174 && x <= 240 && y >= 218 && y <= 284) {
                                listener.onClick(id);
                            }
                        }

                    });
                } catch (Exception exception) {
                    Log.i(TAG_LOG, "CameraFrameLayout onSingleTapUp: exception ");
                }

                return super.onSingleTapUp(e);
            }
        });
    }

    public void setOnClickListener(@NonNull View view, OnClickListener listener) {
        mClickViews.put(view.getId(), listener);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mGestureDetector != null) {
            mGestureDetector.onTouchEvent(event);
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                Log.i(TAG_LOG, "onTouchEvent: CameraFrameLayout");
                SecondaryScreenControlManager.getInstance().removeSecondaryScreenSleepMsg();
                CameraManager.getInstance().removeGoHomeTimeMsg();
                break;
            case MotionEvent.ACTION_UP:
            case ACTION_CANCEL: {
                SecondaryScreenControlManager.getInstance().addSecondaryScreenSleepMsg();
                CameraManager.getInstance().sendGoHomeTimeMsg();
            }
        }
        return true;
    }

    private void init() {
        mLifecycleRegistry = new LifecycleRegistry(this);
        mLifecycleRegistry.setCurrentState(Lifecycle.State.CREATED);
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return mLifecycleRegistry;
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        if (visibility == VISIBLE) {
            mLifecycleRegistry.setCurrentState(Lifecycle.State.RESUMED);
        } else {
            mLifecycleRegistry.setCurrentState(Lifecycle.State.DESTROYED);
        }
    }

    public interface OnClickListener {
        void onClick(int id);
    }
}
