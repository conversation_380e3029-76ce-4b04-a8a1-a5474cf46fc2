<?xml version="1.0" encoding="utf-8"?>
<resources>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <bool name="config_cameraswitch_and_thumbnail_show">true</bool>
    <bool name="config_clock_view_animal_style">false</bool>
    <bool name="config_clock_view_customizable">true</bool>
    <bool name="config_clock_view_select_mode">false</bool>
    <bool name="config_main_layout_scroll_with_touch">true</bool>
    <bool name="config_materialPreferenceIconSpaceReserved">false</bool>
    <bool name="config_subscreen_aod_default">false</bool>
    <bool name="config_subscreen_aod_support">false</bool>
    <bool name="config_subscreen_call_show_default">true</bool>
    <bool name="config_subscreen_charging_turn_on">false</bool>
    <bool name="config_subscreen_compassui_stytle">false</bool>
    <bool name="config_subscreen_contrary_to_screen">false</bool>
    <bool name="config_subscreen_double_tap_default">false</bool>
    <bool name="config_subscreen_double_tap_support">false</bool>
    <bool name="config_subscreen_flip_screenon_support">false</bool>
    <bool name="config_subscreen_main_swicth">true</bool>
    <bool name="config_subscreen_noti_tip_default">false</bool>
    <bool name="config_subscreen_noti_turn_on">false</bool>
    <bool name="config_subscreen_process_support">true</bool>
    <bool name="config_subscreen_racetext_default">false</bool>
    <bool name="config_subscreen_racetext_remove_base">false</bool>
    <bool name="config_subscreen_racetext_support">false</bool>
    <bool name="config_subscreen_remove_calendar">false</bool>
    <bool name="config_subscreen_remove_compassui">false</bool>
    <bool name="config_subscreen_remove_settings_call">false</bool>
    <bool name="config_subscreen_remove_settings_help">false</bool>
    <bool name="config_subscreen_show_2024_doogee_stytle">false</bool>
    <bool name="config_subscreen_show_doogee_stytle">true</bool>
    <bool name="config_tinylcd_reset_real_display_size">false</bool>
    <bool name="is_tablet">false</bool>
    <bool name="racetext_display_battery_default">true</bool>
    <bool name="racetext_display_time_default">true</bool>
</resources>
