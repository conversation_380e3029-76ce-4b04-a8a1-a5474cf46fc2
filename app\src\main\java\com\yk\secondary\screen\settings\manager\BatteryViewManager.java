package com.yk.secondary.screen.settings.manager;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.yk.secondary.screen.settings.utils.Constant.DEFAULT_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.KEY_SUBSCREEN_GOHOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.MSG_GO_HOME_TIME;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.R;
import com.yk.secondary.screen.settings.receiver.BatteryReceiver;
import com.yk.secondary.screen.settings.utils.Constant;
import com.yk.secondary.screen.settings.utils.SPUtils;
import com.yk.secondary.screen.settings.view.BatteryFrameLayout;
import com.yk.secondary.screen.settings.view.MainHorizontalScrollView;

import java.util.concurrent.atomic.AtomicBoolean;

import pl.droidsonroids.gif.GifImageView;

public class BatteryViewManager implements MainHorizontalScrollView.OnDownSlideListener {

    private BatteryViewManager() {
    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final BatteryViewManager instance = new BatteryViewManager();
    }

    public static BatteryViewManager getInstance() {
        return BatteryViewManager.SingletonHolder.instance;
    }

    private BatteryReceiver mBatteryReceiver;

    private ImageView mIVBatteryLevelView;

    private ImageView mIvBattery1, mIvBattery2, mIvBattery3;

    private BatteryFrameLayout mRootView;

    private MainHorizontalScrollView mMainHorizontalScrollView;

    private FrameLayout.LayoutParams mLayoutParams;

    private FrameLayout mChildRootView;

    private GifImageView mGifImageView;

    private ImageView mIvBg;

    private float mBottomMargin = Constant.SCREEN_HEIGHT;//电量距离底部的距离

    public float getBottomMargin() {
        return mBottomMargin;
    }

    public void setBottomMargin(float mBottomMargin) {
        this.mBottomMargin = mBottomMargin;
    }

    private final Handler mGoHomeTimeHandler = new Handler(Looper.getMainLooper(), msg -> {
        if (msg.what == MSG_GO_HOME_TIME) {
            Log.i(TAG_LOG, "BatteryViewManager : mGoHomeTimeHandler MSG_GO_HOME_TIME .... ");
            setBottomMargin(Constant.SCREEN_HEIGHT);
            onDownSlide();
            SecondaryScreenBitmapManager.getInstance().showWatchView();
        }
        return false;
    });

    public void setMainHorizontalScrollView(MainHorizontalScrollView mainHorizontalScrollView) {
        this.mMainHorizontalScrollView = mainHorizontalScrollView;
        this.mMainHorizontalScrollView.setOnSlideListener(this);
    }

    public void setRootView(BatteryFrameLayout mRootView) {
        this.mRootView = mRootView;
        mRootView.setOnSlideListener(this);
        initViews();
        BatteryManager batteryManager = (BatteryManager) App.getInstance().getContext().getSystemService(Context.BATTERY_SERVICE);
        if (batteryManager != null) {
            int batteryLevel = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
            int status = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS);
            boolean isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING || status == BatteryManager.BATTERY_STATUS_FULL;
            updateView(batteryLevel, isCharging);
        }
        registerReceiver();
    }

    public BatteryFrameLayout getRootView() {
        return mRootView;
    }

    //是否完全显示
    private final AtomicBoolean mIsShow = new AtomicBoolean(false);

    public boolean isShow() {
        return mIsShow.get();
    }

    private void setIsShow(boolean show) {
        mIsShow.set(show);
        if (show) {
            sendGoHomeTimeMsg();
            SecondaryScreenBitmapManager.getInstance().removeGoHomeTimeMsg();
        } else {
            SecondaryScreenBitmapManager.getInstance().sendGoHomeTimeMsg();
        }
    }

    @SuppressLint("InflateParams")
    private void initViews() {
        mChildRootView = (FrameLayout) SecondaryScreenBitmapManager.getInstance().getLayoutInflater().inflate(R.layout.view_battery, null);
        mIvBattery1 = mChildRootView.findViewById(R.id.iv_battery_1);
        mIvBattery2 = mChildRootView.findViewById(R.id.iv_battery_2);
        mIvBattery3 = mChildRootView.findViewById(R.id.iv_battery_3);
        mIVBatteryLevelView = mChildRootView.findViewById(R.id.iv_battery_level_bar);
        mGifImageView = mChildRootView.findViewById(R.id.iv_charging);
        mIvBg = mChildRootView.findViewById(R.id.iv_bg);
        if (mRootView != null) {
            mRootView.removeAllViews();
            mLayoutParams = new FrameLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
            mLayoutParams.gravity = Gravity.BOTTOM;
            mLayoutParams.bottomMargin = Constant.SCREEN_HEIGHT;
            mChildRootView.setLayoutParams(mLayoutParams);
            mRootView.addView(mChildRootView);
        }
    }

    private void downSlideBatteryView() {
        mLayoutParams.bottomMargin = (int) mBottomMargin;
        mChildRootView.setLayoutParams(mLayoutParams);

        if (mBottomMargin == 0) {
            setIsShow(true);
        }

        if (mBottomMargin == Constant.SCREEN_HEIGHT) {
            setIsShow(false);
        }
    }

    public void sendGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
        int time = SPUtils.getInstance().getInt(KEY_SUBSCREEN_GOHOME_TIME, DEFAULT_GO_HOME_TIME);
        if (time != -1) {
            mGoHomeTimeHandler.sendEmptyMessageDelayed(MSG_GO_HOME_TIME, time);
        }
    }

    public void removeGoHomeTimeMsg() {
        if (mGoHomeTimeHandler.hasMessages(MSG_GO_HOME_TIME)) {
            mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        }
    }

    @Override
    public void onDownSlide() {
        Log.i(TAG_LOG, "BatteryViewManager onDownSlide: ");
        downSlideBatteryView();
    }

    public void onDestroy() {
        Log.i(TAG_LOG, "BatteryViewManager onDestroy: ");
        unregisterReceiver();
        setBottomMargin(Constant.SCREEN_HEIGHT);
        if (mMainHorizontalScrollView != null) {
            mMainHorizontalScrollView.setOnSlideListener(null);
        }

        mGoHomeTimeHandler.removeMessages(MSG_GO_HOME_TIME);
        mGoHomeTimeHandler.removeCallbacksAndMessages(null);

        if (mRootView != null) {
            mRootView.setOnSlideListener(null);
            mRootView.removeAllViews();
            mRootView.setVisibility(View.GONE);
        }
    }

    private void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_BATTERY_CHANGED);
        if (mBatteryReceiver == null) {
            mBatteryReceiver = new BatteryReceiver();
            mBatteryReceiver.setBatteryStateChangeCallback(new BatteryReceiver.BatteryStateChangeCallback() {
                @SuppressLint("SetTextI18n")
                @Override
                public void onBatteryCurrentLevelChanged(int level, boolean pluggedIn, boolean charging) {
                    updateView(level, charging);
                }
            });
        }
        App.getInstance().getContext().registerReceiver(mBatteryReceiver, filter, Context.RECEIVER_EXPORTED);
    }

    private void unregisterReceiver() {
        if (mBatteryReceiver != null) {
            App.getInstance().getContext().unregisterReceiver(mBatteryReceiver);
            mBatteryReceiver = null;
        }
    }

    private void updateView(int level, boolean charging) {
        Log.i(TAG_LOG, "updateView: level " + level + " charging " + charging);
        if (charging) {
            if (mIVBatteryLevelView != null) {
                mIVBatteryLevelView.setVisibility(GONE);
            }
            if (mGifImageView != null) {
                mGifImageView.setVisibility(VISIBLE);
            }
            if (mIvBg != null) {
                mIvBg.setVisibility(GONE);
            }
        } else {
            if (mGifImageView != null) {
                mGifImageView.setVisibility(GONE);
            }
            if (mIvBg != null) {
                mIvBg.setVisibility(VISIBLE);
            }
            if (mIVBatteryLevelView != null) {
                mIVBatteryLevelView.setVisibility(VISIBLE);
                mIVBatteryLevelView.setImageResource(getBatteryLevelImageResource(level));
            }
        }
        setBatteryLevel(level);
    }

    private void setBatteryLevel(int level) {
        try {
            if (level < 10) {
                if (mIvBattery1 != null) {
                    mIvBattery1.setVisibility(GONE);
                }

                if (mIvBattery2 != null) {
                    mIvBattery2.setVisibility(GONE);
                }
                if (mIvBattery3 != null) {
                    mIvBattery3.setVisibility(VISIBLE);
                    mIvBattery3.setImageResource(getBatteryNumImageResource(String.valueOf(level).charAt(0)));
                }
            } else if (level == 100) {
                if (mIvBattery1 != null) {
                    mIvBattery1.setVisibility(VISIBLE);
                    mIvBattery1.setImageResource(getBatteryNumImageResource(String.valueOf(level).charAt(0)));
                }

                if (mIvBattery2 != null) {
                    mIvBattery2.setVisibility(VISIBLE);
                    mIvBattery2.setImageResource(getBatteryNumImageResource(String.valueOf(level).charAt(1)));
                }
                if (mIvBattery3 != null) {
                    mIvBattery3.setVisibility(VISIBLE);
                    mIvBattery3.setImageResource(getBatteryNumImageResource(String.valueOf(level).charAt(2)));
                }
            } else {
                if (mIvBattery1 != null) {
                    mIvBattery1.setVisibility(GONE);
                }

                if (mIvBattery2 != null) {
                    mIvBattery2.setVisibility(VISIBLE);
                    mIvBattery2.setImageResource(getBatteryNumImageResource(String.valueOf(level).charAt(0)));
                }
                if (mIvBattery3 != null) {
                    mIvBattery3.setVisibility(VISIBLE);
                    mIvBattery3.setImageResource(getBatteryNumImageResource(String.valueOf(level).charAt(1)));
                }
            }
        } catch (Exception e) {
            Log.i(TAG_LOG, "setBattery: Exception " + e.getMessage());
        }
    }

    private int getBatteryNumImageResource(char day) {
        switch (day) {
            case '0':
                return R.drawable.icon_battery_num_0;
            case '1':
                return R.drawable.icon_battery_num_1;
            case '2':
                return R.drawable.icon_battery_num_2;
            case '3':
                return R.drawable.icon_battery_num_3;
            case '4':
                return R.drawable.icon_battery_num_4;
            case '5':
                return R.drawable.icon_battery_num_5;
            case '6':
                return R.drawable.icon_battery_num_6;
            case '7':
                return R.drawable.icon_battery_num_7;
            case '8':
                return R.drawable.icon_battery_num_8;
            case '9':
                return R.drawable.icon_battery_num_9;
            default:
                return R.drawable.icon_battery_num_0;
        }
    }

    private int getBatteryLevelImageResource(int level) {
        if (level == 100) {
            return R.drawable.ic_battery_level_100;
        } else if (level >= 90 && level < 100) {
            return R.drawable.ic_battery_level_90;
        } else if (level >= 80 && level < 90) {
            return R.drawable.ic_battery_level_80;
        } else if (level >= 70 && level < 80) {
            return R.drawable.ic_battery_level_70;
        } else if (level >= 60 && level < 70) {
            return R.drawable.ic_battery_level_60;
        } else if (level >= 50 && level < 60) {
            return R.drawable.ic_battery_level_50;
        } else if (level >= 40 && level < 50) {
            return R.drawable.ic_battery_level_40;
        } else if (level >= 30 && level < 40) {
            return R.drawable.ic_battery_level_30;
        } else if (level >= 20 && level < 30) {
            return R.drawable.ic_battery_level_20;
        } else if (level >= 10 && level < 20) {
            return R.drawable.ic_battery_level_10;
        } else {
            return R.drawable.ic_battery_level_10;
        }
    }
}
