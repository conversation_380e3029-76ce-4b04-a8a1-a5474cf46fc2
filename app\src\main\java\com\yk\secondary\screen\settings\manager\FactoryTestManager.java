package com.yk.secondary.screen.settings.manager;

import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE;
import static com.yk.secondary.screen.settings.manager.SecondaryScreenControlManager.STATUS_SCREEN_CLOSE_IN;
import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL1;
import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL2;
import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL3;
import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL4;
import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL5;
import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL6;
import static com.yk.secondary.screen.settings.utils.Constant.BRIGHTNESS_LEVEL7;
import static com.yk.secondary.screen.settings.utils.Constant.PATH_SECONDARY_BRIGHTNESS_NODES;
import static com.yk.secondary.screen.settings.utils.Constant.TAG_LOG;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.RemoteException;
import android.os.SystemClock;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;

import com.yk.secondary.screen.settings.App;
import com.yk.secondary.screen.settings.IRegisterResultCallback;
import com.yk.secondary.screen.settings.factorytest.LineTestView;
import com.yk.secondary.screen.settings.factorytest.RectItem;
import com.yk.secondary.screen.settings.utils.Constant;
import com.yk.secondary.screen.settings.utils.FileIOUtils;
import com.yk.secondary.screen.settings.utils.ThreadUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class FactoryTestManager {

    private final AtomicBoolean isShow = new AtomicBoolean(false);

    public void setFactoryTest(boolean factoryTest) {
        isShow.set(factoryTest);
        if (!factoryTest) {
            mLineTestView = null;
        }

        if (factoryTest) {
            SecondaryScreenBitmapManager.getInstance().removeGoHomeTimeMsg();
        } else {
            SecondaryScreenBitmapManager.getInstance().sendGoHomeTimeMsg();
        }
    }

    public boolean isFactoryTest() {
        return isShow.get();
    }

    private LineTestView mLineTestView;

    private View mLcdTestView;

    private IRegisterResultCallback mIRegisterResultCallback;

    public void setIRegisterResultCallback(IRegisterResultCallback iRegisterResultCallback) {
        this.mIRegisterResultCallback = iRegisterResultCallback;
    }

    private final int[] sColors = new int[]{
            Color.RED,
            Color.BLUE,
            Color.GREEN,
            Color.WHITE,
            Color.BLACK
    };

    private final int[] sBrightness = new int[]{
            BRIGHTNESS_LEVEL1,
            BRIGHTNESS_LEVEL2,
            BRIGHTNESS_LEVEL3,
            BRIGHTNESS_LEVEL4,
            BRIGHTNESS_LEVEL5,
            BRIGHTNESS_LEVEL6,
            BRIGHTNESS_LEVEL7,
    };

    public LineTestView getFactoryTestLineTestView() {
        return mLineTestView;
    }

    private FrameLayout mRootView;

    public void setRootView(FrameLayout mFactoryTestRootView) {
        this.mRootView = mFactoryTestRootView;
    }

    private FactoryTestManager() {

    }

    private static class SingletonHolder {
        @SuppressLint("StaticFieldLeak")
        private static final FactoryTestManager instance = new FactoryTestManager();
    }

    public static FactoryTestManager getInstance() {
        return FactoryTestManager.SingletonHolder.instance;
    }

    public void startFactoryTest() {
        setFactoryTest(true);
        int screenStatus = SecondaryScreenControlManager.getInstance().getScreenStatus();
        if (screenStatus == STATUS_SCREEN_CLOSE || screenStatus == STATUS_SCREEN_CLOSE_IN) {
            SecondaryScreenControlManager.getInstance().switchSecondaryScreen(true, this::startFactoryTest);
            return;
        }

        if (mRootView == null) {
            setFactoryTest(false);
            return;
        }
        Log.i(TAG_LOG, "FactoryTestManager startFactoryTest: ");
        //划线测试view
        final List<RectItem> items = drawRectItem();
        mLineTestView = new LineTestView(App.getInstance().getContext(), Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT, items);
        mLineTestView.addCallback(() -> {
            Log.i(TAG_LOG, "FactoryTestManager startFactoryTest: mLineTestView  Done ");
            startLCDTest();
        });
        mRootView.removeAllViews();
        mRootView.addView(mLineTestView);
        mRootView.setVisibility(View.VISIBLE);
    }

    /**
     * 开启LCD测试
     */
    private void startLCDTest() {
        Log.i(TAG_LOG, "FactoryTestManager startLCDTest: isMainThread = " + ThreadUtils.isMainThread());
        if (mRootView != null) {
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
            mLcdTestView = new View(App.getInstance().getContext());
            mLcdTestView.setLayoutParams(layoutParams);
            mRootView.addView(mLcdTestView);
            mRootView.removeView(mLineTestView);
            mLineTestView = null;
            for (int i = 0; i < 5; i++) {
                mLcdTestView.setBackgroundColor(sColors[i]);
                if (!isFactoryTest()) {
                    return;
                }
                SystemClock.sleep(2000);
            }

            startLightTest();
        }
    }

    private void startLightTest() {
        Log.i(TAG_LOG, "FactoryTestManager startLightTest: isMainThread = " + ThreadUtils.isMainThread());
        if (mRootView != null) {
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Constant.SCREEN_WIDTH, Constant.SCREEN_HEIGHT);
            View lightTestView = new View(App.getInstance().getContext());
            lightTestView.setLayoutParams(layoutParams);
            lightTestView.setBackgroundColor(Color.WHITE);
            mRootView.addView(lightTestView);
            for (int i = 0; i < 7; i++) {
                if (!isFactoryTest()) {
                    return;
                }
                SystemClock.sleep(2000);
                FileIOUtils.writeFileFromString(PATH_SECONDARY_BRIGHTNESS_NODES, String.valueOf(sBrightness[i]));
            }

            stopFactoryTest();

            if (mIRegisterResultCallback != null) {
                try {
                    mIRegisterResultCallback.onResult(true);
                } catch (RemoteException e) {
                    Log.i(TAG_LOG, "FactoryTestManager startLightTest: RemoteException .... ");
                }
            }
        }
    }

    public void stopFactoryTest() {
        Log.i(TAG_LOG, "FactoryTestManager stopFactoryTest: mFactoryTestRootView " + mRootView);
        if (mRootView != null) {
            setFactoryTest(false);
            mRootView.removeAllViews();
            mLineTestView = null;
            mLcdTestView = null;
            mRootView.setVisibility(View.GONE);
        }
    }

    private List<RectItem> drawRectItem() {
        final List<RectItem> items = new ArrayList<>(0);
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                RectItem rectItem = new RectItem();
                rectItem.rect.left = i * 80;
                rectItem.rect.top = j * 95;
                rectItem.rect.right = 80 * (i + 1);
                rectItem.rect.bottom = 95 * (j + 1);
                items.add(rectItem);
            }
        }
        return items;
    }
}
