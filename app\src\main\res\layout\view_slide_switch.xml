<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="240px"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:focusable="false"
        android:focusableInTouchMode="false" />

    <TextView
        android:id="@+id/tv_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|start"
        android:paddingStart="8px"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:fontFamily="@font/pingfang_regular"
        android:includeFontPadding="false"
        android:textColor="@android:color/white"
        android:textSize="16px" />

    <TextView
        android:id="@+id/tv_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|end"
        android:paddingEnd="8px"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:fontFamily="@font/pingfang_regular"
        android:includeFontPadding="false"
        android:textColor="@android:color/white"
        android:textSize="16px" />

    <ImageView
        android:id="@+id/iv_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />
</FrameLayout>